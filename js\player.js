/**
 * Player class for the Agar.io remake
 */

class Player {
    constructor(x, y, name = 'Anonymous', color = null) {
        this.id = Utils.generateId();
        this.name = Utils.sanitizePlayerName(name);
        this.color = color || Utils.randomColor();
        
        // Position and physics
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        
        // Cell properties
        this.mass = 10;
        this.radius = Utils.radiusFromMass(this.mass);
        this.maxSpeed = Utils.speedFromMass(this.mass);
        
        // Game state
        this.score = 0;
        this.alive = true;
        this.type = 'player';
        
        // Input state
        this.targetX = x;
        this.targetY = y;
        this.moving = false;
        
        // Abilities
        this.canSplit = true;
        this.canEject = true;
        this.splitCooldown = 0;
        this.ejectCooldown = 0;
        
        // Visual effects
        this.pulsePhase = 0;
        this.pulseSpeed = 0.02;
        
        // Statistics
        this.stats = {
            cellsEaten: 0,
            foodEaten: 0,
            timePlayed: 0,
            maxMass: this.mass,
            splits: 0,
            ejects: 0
        };
        
        this.startTime = Date.now();
    }

    /**
     * Update player state
     */
    update(deltaTime, physics) {
        if (!this.alive) return;

        // Update movement towards target
        if (this.moving) {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 5) {
                const force = Math.min(distance * 0.01, this.maxSpeed * 0.1);
                physics.moveTowards(this, this.targetX, this.targetY, force);
            }
        }

        // Update physics
        physics.updateEntity(this, deltaTime);
        physics.limitVelocity(this);

        // Update visual effects
        this.pulsePhase += this.pulseSpeed * deltaTime;

        // Update cooldowns
        this.splitCooldown = Math.max(0, this.splitCooldown - deltaTime);
        this.ejectCooldown = Math.max(0, this.ejectCooldown - deltaTime);

        // Update abilities
        this.canSplit = this.splitCooldown <= 0 && this.mass >= 20;
        this.canEject = this.ejectCooldown <= 0 && this.mass >= 15;

        // Update statistics
        this.stats.timePlayed = (Date.now() - this.startTime) / 1000;
        this.stats.maxMass = Math.max(this.stats.maxMass, this.mass);
        this.score = Math.floor(this.mass * 10);
    }

    /**
     * Set movement target
     */
    setTarget(x, y) {
        this.targetX = x;
        this.targetY = y;
        this.moving = true;
    }

    /**
     * Stop movement
     */
    stopMovement() {
        this.moving = false;
        this.vx *= 0.9;
        this.vy *= 0.9;
    }

    /**
     * Attempt to split the cell
     */
    split(direction, physics) {
        if (!this.canSplit) return null;

        const newCell = physics.splitCell(this, direction);
        if (newCell) {
            this.splitCooldown = 1000; // 1 second cooldown
            this.stats.splits++;
            
            // Mark split time for merge delay
            this.splitTime = Date.now();
            newCell.splitTime = Date.now();
        }

        return newCell;
    }

    /**
     * Eject mass
     */
    ejectMass(direction, physics) {
        if (!this.canEject) return null;

        const ejectedMass = physics.ejectMass(this, direction);
        if (ejectedMass) {
            this.ejectCooldown = 200; // 200ms cooldown
            this.stats.ejects++;
        }

        return ejectedMass;
    }

    /**
     * Eat food or another cell
     */
    eat(target) {
        if (!this.alive || !target) return false;

        // Add mass
        const massGain = target.mass * 0.8; // 80% efficiency
        this.mass += massGain;
        this.radius = Utils.radiusFromMass(this.mass);
        this.maxSpeed = Utils.speedFromMass(this.mass);

        // Update statistics
        if (target.type === 'food') {
            this.stats.foodEaten++;
        } else if (target.type === 'player' || target.type === 'bot') {
            this.stats.cellsEaten++;
        }

        return true;
    }

    /**
     * Kill the player
     */
    die() {
        this.alive = false;
        this.vx = 0;
        this.vy = 0;
    }

    /**
     * Respawn the player
     */
    respawn(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.mass = 10;
        this.radius = Utils.radiusFromMass(this.mass);
        this.maxSpeed = Utils.speedFromMass(this.mass);
        this.alive = true;
        this.splitCooldown = 0;
        this.ejectCooldown = 0;
        this.startTime = Date.now();
        
        // Reset some stats but keep others
        this.stats.cellsEaten = 0;
        this.stats.foodEaten = 0;
        this.stats.maxMass = this.mass;
        this.stats.splits = 0;
        this.stats.ejects = 0;
    }

    /**
     * Render the player
     */
    render(ctx, camera, showName = true) {
        if (!this.alive) return;

        const screenX = (this.x - camera.x) * camera.zoom + camera.width / 2;
        const screenY = (this.y - camera.y) * camera.zoom + camera.height / 2;
        const screenRadius = this.radius * camera.zoom;

        // Skip rendering if outside viewport
        if (screenX + screenRadius < 0 || screenX - screenRadius > camera.width ||
            screenY + screenRadius < 0 || screenY - screenRadius > camera.height) {
            return;
        }

        ctx.save();

        // Draw cell body
        ctx.beginPath();
        ctx.arc(screenX, screenY, screenRadius, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();

        // Draw cell border
        ctx.strokeStyle = '#000';
        ctx.lineWidth = Math.max(1, screenRadius * 0.05);
        ctx.stroke();

        // Draw subtle pulse effect
        const pulseRadius = screenRadius + Math.sin(this.pulsePhase) * 2;
        ctx.beginPath();
        ctx.arc(screenX, screenY, pulseRadius, 0, Math.PI * 2);
        ctx.strokeStyle = this.color + '40';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Draw name if visible and enabled
        if (showName && screenRadius > 20) {
            ctx.fillStyle = '#fff';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.font = `bold ${Math.max(12, screenRadius * 0.3)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            ctx.strokeText(this.name, screenX, screenY);
            ctx.fillText(this.name, screenX, screenY);
        }

        // Draw mass if cell is large enough
        if (screenRadius > 30) {
            const massText = Math.floor(this.mass).toString();
            ctx.fillStyle = '#fff';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.font = `${Math.max(10, screenRadius * 0.2)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const textY = showName ? screenY + 15 : screenY;
            ctx.strokeText(massText, screenX, textY);
            ctx.fillText(massText, screenX, textY);
        }

        ctx.restore();
    }

    /**
     * Check if player is visible in camera view
     */
    isVisible(camera) {
        return Utils.isVisible(this.x, this.y, this.radius, {
            left: camera.x - camera.width / (2 * camera.zoom),
            right: camera.x + camera.width / (2 * camera.zoom),
            top: camera.y - camera.height / (2 * camera.zoom),
            bottom: camera.y + camera.height / (2 * camera.zoom)
        });
    }

    /**
     * Get player data for serialization
     */
    serialize() {
        return {
            id: this.id,
            name: this.name,
            color: this.color,
            x: this.x,
            y: this.y,
            vx: this.vx,
            vy: this.vy,
            mass: this.mass,
            radius: this.radius,
            maxSpeed: this.maxSpeed,
            score: this.score,
            alive: this.alive,
            type: this.type,
            stats: { ...this.stats }
        };
    }

    /**
     * Load player from serialized data
     */
    static deserialize(data) {
        const player = new Player(data.x, data.y, data.name, data.color);
        player.id = data.id;
        player.vx = data.vx;
        player.vy = data.vy;
        player.mass = data.mass;
        player.radius = data.radius;
        player.maxSpeed = data.maxSpeed;
        player.score = data.score;
        player.alive = data.alive;
        player.stats = { ...data.stats };
        return player;
    }

    /**
     * Get distance to another entity
     */
    distanceTo(entity) {
        return Utils.distance(this.x, this.y, entity.x, entity.y);
    }

    /**
     * Get angle to another entity
     */
    angleTo(entity) {
        return Utils.angle(this.x, this.y, entity.x, entity.y);
    }

    /**
     * Check if this player can eat another entity
     */
    canEat(entity) {
        return this.mass / entity.mass >= 1.2;
    }

    /**
     * Get current speed
     */
    getCurrentSpeed() {
        return Math.sqrt(this.vx * this.vx + this.vy * this.vy);
    }

    /**
     * Apply damage (for future power-ups or special mechanics)
     */
    takeDamage(amount) {
        this.mass = Math.max(5, this.mass - amount);
        this.radius = Utils.radiusFromMass(this.mass);
        this.maxSpeed = Utils.speedFromMass(this.mass);
        
        if (this.mass <= 5) {
            this.die();
        }
    }
}

// Export for use in other modules
window.Player = Player;
