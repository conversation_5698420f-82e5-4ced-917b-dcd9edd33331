/**
 * UI management system for the Agar.io remake
 */

class UIManager {
    constructor() {
        this.currentScreen = 'loading';
        this.gameSettings = this.loadSettings();
        this.playerStats = this.loadStats();
        
        // UI elements
        this.elements = {
            screens: {
                loading: document.getElementById('loading-screen'),
                mainMenu: document.getElementById('main-menu'),
                game: document.getElementById('game-screen'),
                settings: document.getElementById('settings-screen'),
                gameOver: document.getElementById('game-over-screen')
            },
            overlays: {
                gameMenu: document.getElementById('game-menu')
            },
            hud: {
                score: document.getElementById('score'),
                mass: document.getElementById('mass'),
                fps: document.getElementById('fps'),
                leaderboard: document.getElementById('leaderboard-list')
            },
            inputs: {
                playerName: document.getElementById('player-name'),
                showGrid: document.getElementById('show-grid'),
                showNames: document.getElementById('show-names'),
                smoothRendering: document.getElementById('smooth-rendering'),
                masterVolume: document.getElementById('master-volume'),
                soundEffects: document.getElementById('sound-effects'),
                mouseSensitivity: document.getElementById('mouse-sensitivity')
            }
        };
        
        // Callbacks
        this.callbacks = {
            startSinglePlayer: [],
            startMultiplayer: [],
            showSettings: [],
            showLeaderboard: [],
            resumeGame: [],
            restartGame: [],
            returnToMenu: [],
            playAgain: []
        };
        
        // Initialize UI
        this.initializeUI();
        this.initializeSkinSelector();
        this.bindEvents();
    }

    /**
     * Initialize UI elements
     */
    initializeUI() {
        // Load saved player name
        const savedName = Utils.loadFromStorage('playerName', '');
        if (savedName && this.elements.inputs.playerName) {
            this.elements.inputs.playerName.value = savedName;
        }
        
        // Load settings
        this.applySettings();
        
        // Show loading screen initially
        this.showScreen('loading');
        
        // Simulate loading time
        setTimeout(() => {
            this.showScreen('mainMenu');
        }, 2000);
    }

    /**
     * Initialize skin selector
     */
    initializeSkinSelector() {
        const skinOptions = document.getElementById('skin-options');
        if (!skinOptions) return;
        
        const skins = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        
        skins.forEach((color, index) => {
            const skinOption = document.createElement('div');
            skinOption.className = 'skin-option';
            skinOption.style.backgroundColor = color;
            skinOption.dataset.color = color;
            
            if (index === 0) {
                skinOption.classList.add('selected');
            }
            
            skinOption.addEventListener('click', () => {
                document.querySelectorAll('.skin-option').forEach(opt => 
                    opt.classList.remove('selected')
                );
                skinOption.classList.add('selected');
            });
            
            skinOptions.appendChild(skinOption);
        });
    }

    /**
     * Bind UI events
     */
    bindEvents() {
        // Main menu buttons
        this.bindButton('play-single', () => this.triggerCallback('startSinglePlayer'));
        this.bindButton('play-multi', () => this.triggerCallback('startMultiplayer'));
        this.bindButton('settings-btn', () => this.showScreen('settings'));
        this.bindButton('leaderboard-btn', () => this.triggerCallback('showLeaderboard'));
        
        // Game menu buttons
        this.bindButton('menu-toggle', () => this.toggleGameMenu());
        this.bindButton('resume-btn', () => this.hideGameMenu());
        this.bindButton('restart-btn', () => this.triggerCallback('restartGame'));
        this.bindButton('main-menu-btn', () => this.triggerCallback('returnToMenu'));
        
        // Settings buttons
        this.bindButton('settings-back', () => this.showScreen('mainMenu'));
        
        // Game over buttons
        this.bindButton('play-again-btn', () => this.triggerCallback('playAgain'));
        this.bindButton('back-to-menu-btn', () => this.triggerCallback('returnToMenu'));
        
        // Settings inputs
        this.bindSettingInput('show-grid', 'showGrid');
        this.bindSettingInput('show-names', 'showNames');
        this.bindSettingInput('smooth-rendering', 'smoothRendering');
        this.bindSettingInput('sound-effects', 'soundEffects');
        this.bindSettingInput('master-volume', 'masterVolume');
        this.bindSettingInput('mouse-sensitivity', 'mouseSensitivity');
        
        // Player name input
        if (this.elements.inputs.playerName) {
            this.elements.inputs.playerName.addEventListener('input', (e) => {
                Utils.saveToStorage('playerName', e.target.value);
            });
        }
    }

    /**
     * Bind button click event
     */
    bindButton(id, callback) {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', callback);
        }
    }

    /**
     * Bind setting input event
     */
    bindSettingInput(id, settingKey) {
        const input = this.elements.inputs[id];
        if (!input) return;
        
        input.addEventListener('change', (e) => {
            const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
            this.gameSettings[settingKey] = value;
            this.saveSettings();
        });
    }

    /**
     * Show specific screen
     */
    showScreen(screenName) {
        // Hide all screens
        Object.values(this.elements.screens).forEach(screen => {
            if (screen) screen.classList.add('hidden');
        });
        
        // Show target screen
        const targetScreen = this.elements.screens[screenName];
        if (targetScreen) {
            targetScreen.classList.remove('hidden');
            this.currentScreen = screenName;
        }
    }

    /**
     * Show game over screen
     */
    showGameOver(stats) {
        const gameOverScreen = this.elements.screens.gameOver;
        if (!gameOverScreen) return;
        
        // Update final stats
        document.getElementById('final-score').textContent = Utils.formatNumber(stats.score || 0);
        document.getElementById('max-mass').textContent = Utils.formatNumber(stats.maxMass || 0);
        document.getElementById('time-played').textContent = Utils.formatTime(stats.timePlayed || 0);
        document.getElementById('cells-eaten').textContent = stats.cellsEaten || 0;
        
        gameOverScreen.classList.remove('hidden');
    }

    /**
     * Hide game over screen
     */
    hideGameOver() {
        const gameOverScreen = this.elements.screens.gameOver;
        if (gameOverScreen) {
            gameOverScreen.classList.add('hidden');
        }
    }

    /**
     * Toggle game menu
     */
    toggleGameMenu() {
        const gameMenu = this.elements.overlays.gameMenu;
        if (gameMenu) {
            gameMenu.classList.toggle('hidden');
        }
    }

    /**
     * Hide game menu
     */
    hideGameMenu() {
        const gameMenu = this.elements.overlays.gameMenu;
        if (gameMenu) {
            gameMenu.classList.add('hidden');
        }
    }

    /**
     * Update HUD elements
     */
    updateHUD(gameState) {
        if (!gameState.player) return;
        
        // Update score
        if (this.elements.hud.score) {
            this.elements.hud.score.textContent = Utils.formatNumber(gameState.player.score);
        }
        
        // Update mass
        if (this.elements.hud.mass) {
            this.elements.hud.mass.textContent = Math.floor(gameState.player.mass);
        }
        
        // Update FPS
        if (this.elements.hud.fps && gameState.renderer) {
            this.elements.hud.fps.textContent = gameState.renderer.getFPS();
        }
        
        // Update leaderboard
        this.updateLeaderboard(gameState.leaderboard || []);
    }

    /**
     * Update leaderboard
     */
    updateLeaderboard(leaderboard) {
        const leaderboardList = this.elements.hud.leaderboard;
        if (!leaderboardList) return;
        
        leaderboardList.innerHTML = '';
        
        leaderboard.slice(0, 10).forEach((entry, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <span class="leaderboard-player">${index + 1}. ${entry.name}</span>
                <span class="leaderboard-score">${Utils.formatNumber(entry.score)}</span>
            `;
            leaderboardList.appendChild(li);
        });
    }

    /**
     * Get player name from input
     */
    getPlayerName() {
        const nameInput = this.elements.inputs.playerName;
        return nameInput ? Utils.sanitizePlayerName(nameInput.value) : 'Anonymous';
    }

    /**
     * Get selected skin color
     */
    getSelectedSkin() {
        const selectedSkin = document.querySelector('.skin-option.selected');
        return selectedSkin ? selectedSkin.dataset.color : '#FF6B6B';
    }

    /**
     * Apply settings to UI
     */
    applySettings() {
        Object.entries(this.gameSettings).forEach(([key, value]) => {
            const input = this.elements.inputs[key];
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = value;
                } else {
                    input.value = value;
                }
            }
        });
    }

    /**
     * Load settings from storage
     */
    loadSettings() {
        return Utils.loadFromStorage('gameSettings', {
            showGrid: true,
            showNames: true,
            smoothRendering: true,
            soundEffects: true,
            masterVolume: 50,
            mouseSensitivity: 1.0
        });
    }

    /**
     * Save settings to storage
     */
    saveSettings() {
        Utils.saveToStorage('gameSettings', this.gameSettings);
    }

    /**
     * Load player statistics
     */
    loadStats() {
        return Utils.loadFromStorage('playerStats', {
            gamesPlayed: 0,
            totalScore: 0,
            bestScore: 0,
            totalTimePlayed: 0,
            totalCellsEaten: 0
        });
    }

    /**
     * Save player statistics
     */
    saveStats() {
        Utils.saveToStorage('playerStats', this.playerStats);
    }

    /**
     * Update player statistics
     */
    updateStats(gameStats) {
        this.playerStats.gamesPlayed++;
        this.playerStats.totalScore += gameStats.score || 0;
        this.playerStats.bestScore = Math.max(this.playerStats.bestScore, gameStats.score || 0);
        this.playerStats.totalTimePlayed += gameStats.timePlayed || 0;
        this.playerStats.totalCellsEaten += gameStats.cellsEaten || 0;
        
        this.saveStats();
    }

    /**
     * Add callback for UI events
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Trigger callback
     */
    triggerCallback(event, ...args) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(...args));
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }

    /**
     * Get current settings
     */
    getSettings() {
        return { ...this.gameSettings };
    }
}

// Export for use in other modules
window.UIManager = UIManager;
