/**
 * Renderer class for the Agar.io remake
 */

class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            width: canvas.width,
            height: canvas.height,
            targetZoom: 1,
            smoothing: 0.1
        };
        
        // Rendering settings
        this.settings = {
            showGrid: true,
            showNames: true,
            smoothRendering: true,
            antiAliasing: true,
            particleEffects: true
        };
        
        // Grid properties
        this.gridSize = 50;
        this.gridColor = '#333';
        this.gridLineWidth = 1;
        
        // Performance tracking
        this.frameCount = 0;
        this.lastFpsUpdate = Date.now();
        this.fps = 60;
        
        // Background
        this.backgroundColor = '#1a1a1a';
        
        // Initialize canvas
        this.initCanvas();
    }

    /**
     * Initialize canvas settings
     */
    initCanvas() {
        // Set canvas size to viewport
        this.resize();
        
        // Enable anti-aliasing if supported
        if (this.settings.antiAliasing) {
            this.ctx.imageSmoothingEnabled = true;
            this.ctx.imageSmoothingQuality = 'high';
        }
        
        // Set default styles
        this.ctx.textBaseline = 'middle';
        this.ctx.textAlign = 'center';
    }

    /**
     * Resize canvas to fit viewport
     */
    resize() {
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        // Set actual canvas size
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        // Scale canvas back down using CSS
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // Scale context to match device pixel ratio
        this.ctx.scale(dpr, dpr);
        
        // Update camera dimensions
        this.camera.width = rect.width;
        this.camera.height = rect.height;
    }

    /**
     * Update camera position and zoom
     */
    updateCamera(targetX, targetY, targetZoom = null) {
        // Smooth camera movement
        const smoothing = this.camera.smoothing;
        this.camera.x += (targetX - this.camera.x) * smoothing;
        this.camera.y += (targetY - this.camera.y) * smoothing;
        
        // Smooth zoom
        if (targetZoom !== null) {
            this.camera.targetZoom = targetZoom;
        }
        this.camera.zoom += (this.camera.targetZoom - this.camera.zoom) * smoothing;
    }

    /**
     * Set camera zoom based on player mass
     */
    setCameraZoom(playerMass) {
        // Zoom out as player gets larger
        const baseZoom = 1;
        const zoomFactor = Math.max(0.3, baseZoom - Math.log(playerMass) * 0.1);
        this.camera.targetZoom = zoomFactor;
    }

    /**
     * Clear the canvas
     */
    clear() {
        this.ctx.fillStyle = this.backgroundColor;
        this.ctx.fillRect(0, 0, this.camera.width, this.camera.height);
    }

    /**
     * Render grid background
     */
    renderGrid(worldWidth, worldHeight) {
        if (!this.settings.showGrid) return;

        const ctx = this.ctx;
        const camera = this.camera;
        
        // Calculate grid bounds
        const startX = Math.floor((camera.x - camera.width / (2 * camera.zoom)) / this.gridSize) * this.gridSize;
        const endX = Math.ceil((camera.x + camera.width / (2 * camera.zoom)) / this.gridSize) * this.gridSize;
        const startY = Math.floor((camera.y - camera.height / (2 * camera.zoom)) / this.gridSize) * this.gridSize;
        const endY = Math.ceil((camera.y + camera.height / (2 * camera.zoom)) / this.gridSize) * this.gridSize;

        ctx.save();
        ctx.strokeStyle = this.gridColor;
        ctx.lineWidth = this.gridLineWidth;
        ctx.globalAlpha = 0.3;

        // Draw vertical lines
        for (let x = startX; x <= endX; x += this.gridSize) {
            if (x >= 0 && x <= worldWidth) {
                const screenX = (x - camera.x) * camera.zoom + camera.width / 2;
                ctx.beginPath();
                ctx.moveTo(screenX, 0);
                ctx.lineTo(screenX, camera.height);
                ctx.stroke();
            }
        }

        // Draw horizontal lines
        for (let y = startY; y <= endY; y += this.gridSize) {
            if (y >= 0 && y <= worldHeight) {
                const screenY = (y - camera.y) * camera.zoom + camera.height / 2;
                ctx.beginPath();
                ctx.moveTo(0, screenY);
                ctx.lineTo(camera.width, screenY);
                ctx.stroke();
            }
        }

        ctx.restore();
    }

    /**
     * Render world boundaries
     */
    renderBoundaries(worldWidth, worldHeight) {
        const ctx = this.ctx;
        const camera = this.camera;
        
        // Calculate boundary positions on screen
        const leftX = (0 - camera.x) * camera.zoom + camera.width / 2;
        const rightX = (worldWidth - camera.x) * camera.zoom + camera.width / 2;
        const topY = (0 - camera.y) * camera.zoom + camera.height / 2;
        const bottomY = (worldHeight - camera.y) * camera.zoom + camera.height / 2;

        ctx.save();
        ctx.strokeStyle = '#ff4444';
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.8;

        // Draw boundaries
        ctx.beginPath();
        ctx.rect(leftX, topY, rightX - leftX, bottomY - topY);
        ctx.stroke();

        ctx.restore();
    }

    /**
     * Convert world coordinates to screen coordinates
     */
    worldToScreen(worldX, worldY) {
        return {
            x: (worldX - this.camera.x) * this.camera.zoom + this.camera.width / 2,
            y: (worldY - this.camera.y) * this.camera.zoom + this.camera.height / 2
        };
    }

    /**
     * Convert screen coordinates to world coordinates
     */
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.camera.width / 2) / this.camera.zoom + this.camera.x,
            y: (screenY - this.camera.height / 2) / this.camera.zoom + this.camera.y
        };
    }

    /**
     * Check if object is visible in current view
     */
    isVisible(x, y, radius) {
        const screen = this.worldToScreen(x, y);
        const screenRadius = radius * this.camera.zoom;
        
        return screen.x + screenRadius >= 0 &&
               screen.x - screenRadius <= this.camera.width &&
               screen.y + screenRadius >= 0 &&
               screen.y - screenRadius <= this.camera.height;
    }

    /**
     * Render all game entities
     */
    render(gameState) {
        // Clear canvas
        this.clear();

        // Render grid
        this.renderGrid(gameState.worldWidth, gameState.worldHeight);

        // Render boundaries
        this.renderBoundaries(gameState.worldWidth, gameState.worldHeight);

        // Render food
        if (gameState.foodManager) {
            gameState.foodManager.render(this.ctx, this.camera);
        }

        // Render ejected mass
        if (gameState.ejectedMass) {
            this.renderEjectedMass(gameState.ejectedMass);
        }

        // Render players and bots (sorted by size for proper layering)
        const allCells = [...(gameState.players || []), ...(gameState.bots || [])];
        allCells.sort((a, b) => a.radius - b.radius);
        
        for (const cell of allCells) {
            if (cell.alive && this.isVisible(cell.x, cell.y, cell.radius)) {
                cell.render(this.ctx, this.camera, this.settings.showNames);
            }
        }

        // Update FPS counter
        this.updateFPS();
    }

    /**
     * Render ejected mass
     */
    renderEjectedMass(ejectedMass) {
        for (const mass of ejectedMass) {
            if (this.isVisible(mass.x, mass.y, mass.radius)) {
                const screen = this.worldToScreen(mass.x, mass.y);
                const screenRadius = mass.radius * this.camera.zoom;

                this.ctx.save();
                this.ctx.beginPath();
                this.ctx.arc(screen.x, screen.y, screenRadius, 0, Math.PI * 2);
                this.ctx.fillStyle = mass.color;
                this.ctx.fill();
                this.ctx.strokeStyle = '#000';
                this.ctx.lineWidth = 1;
                this.ctx.stroke();
                this.ctx.restore();
            }
        }
    }

    /**
     * Update FPS counter
     */
    updateFPS() {
        this.frameCount++;
        const now = Date.now();
        
        if (now - this.lastFpsUpdate >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = now;
        }
    }

    /**
     * Get current FPS
     */
    getFPS() {
        return this.fps;
    }

    /**
     * Update renderer settings
     */
    updateSettings(newSettings) {
        Object.assign(this.settings, newSettings);
        
        // Apply anti-aliasing setting
        this.ctx.imageSmoothingEnabled = this.settings.antiAliasing;
    }

    /**
     * Take screenshot of current canvas
     */
    takeScreenshot() {
        return this.canvas.toDataURL('image/png');
    }
}

// Export for use in other modules
window.Renderer = Renderer;
