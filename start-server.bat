@echo off
echo 🎮 Agar.io Remake - Local Server
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.x
    echo 📥 Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if index.html exists
if not exist "index.html" (
    echo ❌ index.html not found!
    echo 📁 Make sure you're in the correct directory
    pause
    exit /b 1
)

echo ✅ Starting local server...
echo 🌐 The game will open in your browser automatically
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start the Python server
python server.py

pause
