/**
 * Main Game class for the Agar.io remake
 */

class Game {
    constructor() {
        // Game state
        this.state = 'menu'; // menu, playing, paused, gameOver
        this.mode = 'single'; // single, multi
        
        // World properties
        this.worldWidth = 3000;
        this.worldHeight = 3000;
        
        // Game entities
        this.player = null;
        this.bots = [];
        this.ejectedMass = [];
        this.foodManager = null;
        
        // Game systems
        this.physics = null;
        this.renderer = null;
        this.inputManager = null;
        this.uiManager = null;
        
        // Game loop
        this.lastTime = 0;
        this.deltaTime = 0;
        this.gameLoopId = null;
        this.targetFPS = 60;
        this.frameTime = 1000 / this.targetFPS;
        
        // Game settings
        this.maxBots = 15;
        this.botDifficulties = ['easy', 'medium', 'hard'];
        
        // Statistics
        this.gameStartTime = 0;
        this.leaderboard = [];
    }

    /**
     * Initialize the game
     */
    init() {
        // Initialize canvas
        const canvas = document.getElementById('game-canvas');
        if (!canvas) {
            console.error('Game canvas not found!');
            return;
        }

        // Initialize game systems
        this.physics = new Physics(this.worldWidth, this.worldHeight);
        this.renderer = new Renderer(canvas);
        this.inputManager = new InputManager(canvas, this.renderer);
        this.uiManager = new UIManager();
        this.foodManager = new FoodManager(this.worldWidth, this.worldHeight);

        // Bind UI events
        this.bindUIEvents();
        
        // Bind input events
        this.bindInputEvents();
        
        // Initialize food
        this.foodManager.init();
        
        console.log('Game initialized successfully!');
    }

    /**
     * Bind UI events
     */
    bindUIEvents() {
        this.uiManager.on('startSinglePlayer', () => this.startSinglePlayer());
        this.uiManager.on('startMultiplayer', () => this.startMultiplayer());
        this.uiManager.on('resumeGame', () => this.resumeGame());
        this.uiManager.on('restartGame', () => this.restartGame());
        this.uiManager.on('returnToMenu', () => this.returnToMenu());
        this.uiManager.on('playAgain', () => this.playAgain());
    }

    /**
     * Bind input events
     */
    bindInputEvents() {
        this.inputManager.on('move', (worldX, worldY) => {
            if (this.player && this.state === 'playing') {
                this.player.setTarget(worldX, worldY);
            }
        });

        this.inputManager.on('split', () => {
            if (this.player && this.state === 'playing') {
                this.handlePlayerSplit();
            }
        });

        this.inputManager.on('eject', () => {
            if (this.player && this.state === 'playing') {
                this.handlePlayerEject();
            }
        });

        this.inputManager.on('menu', () => {
            if (this.state === 'playing') {
                this.pauseGame();
            }
        });
    }

    /**
     * Start single player game
     */
    startSinglePlayer() {
        this.mode = 'single';
        this.startGame();
    }

    /**
     * Start multiplayer game
     */
    startMultiplayer() {
        this.mode = 'multi';
        this.uiManager.showNotification('Multiplayer not yet implemented!', 'info');
        // TODO: Implement multiplayer
    }

    /**
     * Start the game
     */
    startGame() {
        // Reset game state
        this.resetGame();
        
        // Create player
        const playerName = this.uiManager.getPlayerName();
        const playerColor = this.uiManager.getSelectedSkin();
        const spawnPos = this.getRandomSpawnPosition();
        
        this.player = new Player(spawnPos.x, spawnPos.y, playerName, playerColor);
        
        // Create bots for single player
        if (this.mode === 'single') {
            this.createBots();
        }
        
        // Update renderer settings
        const settings = this.uiManager.getSettings();
        this.renderer.updateSettings(settings);
        
        // Update input sensitivity
        this.inputManager.setMouseSensitivity(settings.mouseSensitivity);
        
        // Start game loop
        this.state = 'playing';
        this.gameStartTime = Date.now();
        this.uiManager.showScreen('game');
        this.startGameLoop();
        
        console.log('Game started!');
    }

    /**
     * Reset game state
     */
    resetGame() {
        this.player = null;
        this.bots = [];
        this.ejectedMass = [];
        this.leaderboard = [];
        
        // Reset food
        this.foodManager.clear();
        this.foodManager.init();
        
        // Stop existing game loop
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
    }

    /**
     * Create AI bots
     */
    createBots() {
        this.bots = [];
        
        for (let i = 0; i < this.maxBots; i++) {
            const spawnPos = this.getRandomSpawnPosition();
            const difficulty = this.botDifficulties[Utils.randomInt(0, this.botDifficulties.length - 1)];
            
            const bot = new Bot(spawnPos.x, spawnPos.y, difficulty);
            this.bots.push(bot);
        }
    }

    /**
     * Get random spawn position
     */
    getRandomSpawnPosition() {
        const margin = 100;
        return {
            x: Utils.random(margin, this.worldWidth - margin),
            y: Utils.random(margin, this.worldHeight - margin)
        };
    }

    /**
     * Start game loop
     */
    startGameLoop() {
        const gameLoop = (currentTime) => {
            this.deltaTime = currentTime - this.lastTime;
            this.lastTime = currentTime;
            
            // Limit delta time to prevent large jumps
            this.deltaTime = Math.min(this.deltaTime, this.frameTime * 2);
            
            this.update(this.deltaTime);
            this.render();
            
            if (this.state === 'playing') {
                this.gameLoopId = requestAnimationFrame(gameLoop);
            }
        };
        
        this.lastTime = performance.now();
        this.gameLoopId = requestAnimationFrame(gameLoop);
    }

    /**
     * Update game state
     */
    update(deltaTime) {
        if (this.state !== 'playing') return;

        // Update player
        if (this.player && this.player.alive) {
            this.player.update(deltaTime, this.physics);
            
            // Update camera
            this.renderer.updateCamera(this.player.x, this.player.y);
            this.renderer.setCameraZoom(this.player.mass);
        } else if (this.player && !this.player.alive) {
            // Player died
            this.handlePlayerDeath();
        }

        // Update bots
        const allEntities = this.getAllEntities();
        for (const bot of this.bots) {
            if (bot.alive) {
                bot.update(deltaTime, this.physics, allEntities);
            }
        }

        // Update food
        this.foodManager.update(deltaTime);

        // Update ejected mass
        this.updateEjectedMass(deltaTime);

        // Handle collisions
        this.handleCollisions();

        // Update leaderboard
        this.updateLeaderboard();

        // Update UI
        this.uiManager.updateHUD({
            player: this.player,
            renderer: this.renderer,
            leaderboard: this.leaderboard
        });
    }

    /**
     * Render game
     */
    render() {
        const gameState = {
            worldWidth: this.worldWidth,
            worldHeight: this.worldHeight,
            players: this.player ? [this.player] : [],
            bots: this.bots,
            foodManager: this.foodManager,
            ejectedMass: this.ejectedMass
        };
        
        this.renderer.render(gameState);
    }

    /**
     * Get all entities for collision detection
     */
    getAllEntities() {
        const entities = [];
        
        if (this.player) entities.push(this.player);
        entities.push(...this.bots);
        entities.push(...this.foodManager.getAllFoods());
        entities.push(...this.ejectedMass);
        
        return entities;
    }

    /**
     * Handle collisions
     */
    handleCollisions() {
        const allCells = [];
        if (this.player && this.player.alive) allCells.push(this.player);
        allCells.push(...this.bots.filter(bot => bot.alive));

        // Cell vs Food collisions
        for (const cell of allCells) {
            const nearbyFood = this.foodManager.getFoodsInArea(
                cell.x - cell.radius - 50,
                cell.y - cell.radius - 50,
                (cell.radius + 50) * 2,
                (cell.radius + 50) * 2
            );

            for (const food of nearbyFood) {
                const collision = this.physics.checkCollision(cell, food);
                if (collision.colliding) {
                    cell.eat(food);
                    this.foodManager.removeFood(food.id);
                }
            }
        }

        // Cell vs Cell collisions
        for (let i = 0; i < allCells.length; i++) {
            for (let j = i + 1; j < allCells.length; j++) {
                const cell1 = allCells[i];
                const cell2 = allCells[j];
                
                if (this.physics.handleEating(cell1, cell2)) {
                    cell2.die();
                } else if (this.physics.handleEating(cell2, cell1)) {
                    cell1.die();
                }
            }
        }

        // Cell vs Ejected Mass collisions
        for (const cell of allCells) {
            for (let i = this.ejectedMass.length - 1; i >= 0; i--) {
                const mass = this.ejectedMass[i];
                const collision = this.physics.checkCollision(cell, mass);
                
                if (collision.colliding && cell.id !== mass.playerId) {
                    cell.eat(mass);
                    this.ejectedMass.splice(i, 1);
                }
            }
        }
    }

    /**
     * Update ejected mass
     */
    updateEjectedMass(deltaTime) {
        for (let i = this.ejectedMass.length - 1; i >= 0; i--) {
            const mass = this.ejectedMass[i];
            this.physics.updateEjectedMass(mass, deltaTime);
            
            // Convert to food if lifetime expired
            if (mass.type === 'food') {
                this.foodManager.spawnFoodAt(mass.x, mass.y, mass.mass);
                this.ejectedMass.splice(i, 1);
            }
        }
    }

    /**
     * Handle player split
     */
    handlePlayerSplit() {
        if (!this.player || !this.player.canSplit) return;
        
        const mousePos = this.inputManager.getMousePosition();
        const direction = Utils.angle(this.player.x, this.player.y, mousePos.world.x, mousePos.world.y);
        
        const newCell = this.player.split(direction, this.physics);
        if (newCell) {
            // For now, just merge back immediately (simplified)
            // In a full implementation, you'd track multiple cells per player
            console.log('Player split!');
        }
    }

    /**
     * Handle player eject
     */
    handlePlayerEject() {
        if (!this.player || !this.player.canEject) return;
        
        const mousePos = this.inputManager.getMousePosition();
        const direction = Utils.angle(this.player.x, this.player.y, mousePos.world.x, mousePos.world.y);
        
        const ejectedMass = this.player.ejectMass(direction, this.physics);
        if (ejectedMass) {
            ejectedMass.playerId = this.player.id;
            this.ejectedMass.push(ejectedMass);
        }
    }

    /**
     * Handle player death
     */
    handlePlayerDeath() {
        this.state = 'gameOver';
        
        // Update statistics
        this.uiManager.updateStats(this.player.stats);
        
        // Show game over screen
        this.uiManager.showGameOver(this.player.stats);
        
        console.log('Player died!');
    }

    /**
     * Update leaderboard
     */
    updateLeaderboard() {
        this.leaderboard = [];
        
        if (this.player && this.player.alive) {
            this.leaderboard.push({
                name: this.player.name,
                score: this.player.score,
                isPlayer: true
            });
        }
        
        for (const bot of this.bots) {
            if (bot.alive) {
                this.leaderboard.push({
                    name: bot.name,
                    score: bot.score,
                    isPlayer: false
                });
            }
        }
        
        // Sort by score
        this.leaderboard.sort((a, b) => b.score - a.score);
    }

    /**
     * Pause game
     */
    pauseGame() {
        if (this.state === 'playing') {
            this.state = 'paused';
            this.uiManager.toggleGameMenu();
        }
    }

    /**
     * Resume game
     */
    resumeGame() {
        if (this.state === 'paused') {
            this.state = 'playing';
            this.uiManager.hideGameMenu();
            this.startGameLoop();
        }
    }

    /**
     * Restart game
     */
    restartGame() {
        this.uiManager.hideGameMenu();
        this.startGame();
    }

    /**
     * Return to main menu
     */
    returnToMenu() {
        this.state = 'menu';
        this.uiManager.hideGameMenu();
        this.uiManager.hideGameOver();
        this.uiManager.showScreen('mainMenu');
        
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
    }

    /**
     * Play again
     */
    playAgain() {
        this.uiManager.hideGameOver();
        this.startGame();
    }
}

// Export for use in other modules
window.Game = Game;
