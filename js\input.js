/**
 * Input handling system for the Agar.io remake
 */

class InputManager {
    constructor(canvas, renderer) {
        this.canvas = canvas;
        this.renderer = renderer;
        
        // Input state
        this.mouse = {
            x: 0,
            y: 0,
            worldX: 0,
            worldY: 0,
            isDown: false,
            button: -1
        };
        
        this.keys = new Set();
        this.touches = new Map();
        
        // Mobile controls
        this.joystick = {
            active: false,
            centerX: 0,
            centerY: 0,
            knobX: 0,
            knobY: 0,
            maxDistance: 60,
            deadzone: 10
        };
        
        // Settings
        this.mouseSensitivity = 1.0;
        this.isMobile = Utils.isMobile();
        
        // Event callbacks
        this.callbacks = {
            move: [],
            split: [],
            eject: [],
            menu: []
        };
        
        // Initialize event listeners
        this.initEventListeners();
        this.initMobileControls();
    }

    /**
     * Initialize event listeners
     */
    initEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
        this.canvas.addEventListener('contextmenu', e => e.preventDefault());
        
        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        
        // Window events
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('blur', this.handleWindowBlur.bind(this));
    }

    /**
     * Initialize mobile controls
     */
    initMobileControls() {
        if (!this.isMobile) return;

        // Joystick controls
        const joystick = document.getElementById('movement-joystick');
        const knob = document.getElementById('joystick-knob');
        
        if (joystick && knob) {
            joystick.addEventListener('touchstart', this.handleJoystickStart.bind(this));
            joystick.addEventListener('touchmove', this.handleJoystickMove.bind(this));
            joystick.addEventListener('touchend', this.handleJoystickEnd.bind(this));
        }

        // Action buttons
        const splitBtn = document.getElementById('split-btn');
        const ejectBtn = document.getElementById('eject-btn');
        
        if (splitBtn) {
            splitBtn.addEventListener('touchstart', () => this.triggerSplit());
        }
        
        if (ejectBtn) {
            ejectBtn.addEventListener('touchstart', () => this.triggerEject());
        }
    }

    /**
     * Handle mouse movement
     */
    handleMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;
        
        // Convert to world coordinates
        const worldPos = this.renderer.screenToWorld(this.mouse.x, this.mouse.y);
        this.mouse.worldX = worldPos.x;
        this.mouse.worldY = worldPos.y;
        
        // Trigger move callbacks
        this.triggerMove(this.mouse.worldX, this.mouse.worldY);
    }

    /**
     * Handle mouse down
     */
    handleMouseDown(event) {
        this.mouse.isDown = true;
        this.mouse.button = event.button;
        
        // Right click or middle click to split
        if (event.button === 2 || event.button === 1) {
            this.triggerSplit();
        }
    }

    /**
     * Handle mouse up
     */
    handleMouseUp(event) {
        this.mouse.isDown = false;
        this.mouse.button = -1;
    }

    /**
     * Handle key down
     */
    handleKeyDown(event) {
        this.keys.add(event.code);
        
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.triggerSplit();
                break;
            case 'KeyW':
                event.preventDefault();
                this.triggerEject();
                break;
            case 'Escape':
                this.triggerMenu();
                break;
        }
    }

    /**
     * Handle key up
     */
    handleKeyUp(event) {
        this.keys.delete(event.code);
    }

    /**
     * Handle touch start
     */
    handleTouchStart(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            this.touches.set(touch.identifier, {
                x: touch.clientX,
                y: touch.clientY,
                startTime: Date.now()
            });
        }
        
        // Use first touch for movement
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const rect = this.canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            const worldPos = this.renderer.screenToWorld(x, y);
            this.triggerMove(worldPos.x, worldPos.y);
        }
    }

    /**
     * Handle touch move
     */
    handleTouchMove(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            if (this.touches.has(touch.identifier)) {
                this.touches.set(touch.identifier, {
                    x: touch.clientX,
                    y: touch.clientY,
                    startTime: this.touches.get(touch.identifier).startTime
                });
            }
        }
        
        // Use first touch for movement
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const rect = this.canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            const worldPos = this.renderer.screenToWorld(x, y);
            this.triggerMove(worldPos.x, worldPos.y);
        }
    }

    /**
     * Handle touch end
     */
    handleTouchEnd(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            const touchData = this.touches.get(touch.identifier);
            if (touchData) {
                const duration = Date.now() - touchData.startTime;
                
                // Quick tap to split
                if (duration < 200) {
                    this.triggerSplit();
                }
                
                this.touches.delete(touch.identifier);
            }
        }
    }

    /**
     * Handle joystick start
     */
    handleJoystickStart(event) {
        event.preventDefault();
        const rect = event.currentTarget.getBoundingClientRect();
        this.joystick.centerX = rect.left + rect.width / 2;
        this.joystick.centerY = rect.top + rect.height / 2;
        this.joystick.active = true;
    }

    /**
     * Handle joystick move
     */
    handleJoystickMove(event) {
        if (!this.joystick.active) return;
        event.preventDefault();
        
        const touch = event.touches[0];
        const dx = touch.clientX - this.joystick.centerX;
        const dy = touch.clientY - this.joystick.centerY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > this.joystick.deadzone) {
            // Limit to max distance
            const limitedDistance = Math.min(distance, this.joystick.maxDistance);
            const angle = Math.atan2(dy, dx);
            
            this.joystick.knobX = Math.cos(angle) * limitedDistance;
            this.joystick.knobY = Math.sin(angle) * limitedDistance;
            
            // Update knob position
            const knob = document.getElementById('joystick-knob');
            if (knob) {
                knob.style.transform = `translate(${this.joystick.knobX}px, ${this.joystick.knobY}px)`;
            }
            
            // Calculate movement direction
            const normalizedX = this.joystick.knobX / this.joystick.maxDistance;
            const normalizedY = this.joystick.knobY / this.joystick.maxDistance;
            
            // Convert to world coordinates relative to camera
            const camera = this.renderer.camera;
            const worldX = camera.x + normalizedX * 200;
            const worldY = camera.y + normalizedY * 200;
            
            this.triggerMove(worldX, worldY);
        }
    }

    /**
     * Handle joystick end
     */
    handleJoystickEnd(event) {
        event.preventDefault();
        this.joystick.active = false;
        this.joystick.knobX = 0;
        this.joystick.knobY = 0;
        
        // Reset knob position
        const knob = document.getElementById('joystick-knob');
        if (knob) {
            knob.style.transform = 'translate(0px, 0px)';
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        this.renderer.resize();
    }

    /**
     * Handle window blur (pause game)
     */
    handleWindowBlur() {
        this.keys.clear();
        this.mouse.isDown = false;
    }

    /**
     * Trigger movement callback
     */
    triggerMove(worldX, worldY) {
        for (const callback of this.callbacks.move) {
            callback(worldX, worldY);
        }
    }

    /**
     * Trigger split callback
     */
    triggerSplit() {
        for (const callback of this.callbacks.split) {
            callback();
        }
    }

    /**
     * Trigger eject callback
     */
    triggerEject() {
        for (const callback of this.callbacks.eject) {
            callback();
        }
    }

    /**
     * Trigger menu callback
     */
    triggerMenu() {
        for (const callback of this.callbacks.menu) {
            callback();
        }
    }

    /**
     * Add event callback
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Remove event callback
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * Check if key is pressed
     */
    isKeyPressed(keyCode) {
        return this.keys.has(keyCode);
    }

    /**
     * Get mouse position
     */
    getMousePosition() {
        return {
            screen: { x: this.mouse.x, y: this.mouse.y },
            world: { x: this.mouse.worldX, y: this.mouse.worldY }
        };
    }

    /**
     * Update mouse sensitivity
     */
    setMouseSensitivity(sensitivity) {
        this.mouseSensitivity = Math.max(0.1, Math.min(2.0, sensitivity));
    }

    /**
     * Cleanup event listeners
     */
    destroy() {
        // Remove all event listeners
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('mousedown', this.handleMouseDown);
        this.canvas.removeEventListener('mouseup', this.handleMouseUp);
        
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        
        this.canvas.removeEventListener('touchstart', this.handleTouchStart);
        this.canvas.removeEventListener('touchmove', this.handleTouchMove);
        this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('blur', this.handleWindowBlur);
    }
}

// Export for use in other modules
window.InputManager = InputManager;
