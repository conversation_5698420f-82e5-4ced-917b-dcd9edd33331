/* Mobile-specific styles */
@media (max-width: 768px) {
    .mobile-only {
        display: block !important;
    }

    /* Mobile Controls */
    #mobile-controls {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 200px;
        z-index: 200;
        pointer-events: none;
    }

    #mobile-controls > * {
        pointer-events: auto;
    }

    /* Movement Joystick */
    #movement-joystick {
        position: absolute;
        bottom: 30px;
        left: 30px;
        width: 120px;
        height: 120px;
        background: rgba(255, 255, 255, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #joystick-knob {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        transition: all 0.1s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    /* Action Buttons */
    #action-buttons {
        position: absolute;
        bottom: 30px;
        right: 30px;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .action-btn {
        width: 70px;
        height: 70px;
        border: none;
        border-radius: 50%;
        background: rgba(52, 152, 219, 0.8);
        color: white;
        font-size: 0.9rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
    }

    .action-btn:active {
        transform: scale(0.95);
        background: rgba(41, 128, 185, 0.9);
    }

    #split-btn {
        background: rgba(231, 76, 60, 0.8);
    }

    #split-btn:active {
        background: rgba(192, 57, 43, 0.9);
    }

    /* Mobile Menu Adjustments */
    .menu-content {
        padding: 20px;
    }

    .menu-content h1 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .menu-btn {
        padding: 18px 20px;
        font-size: 1.2rem;
        margin: 12px 0;
    }

    /* Mobile HUD Adjustments */
    #top-hud {
        top: 10px;
        left: 10px;
        right: 10px;
        flex-direction: row;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 8px;
    }

    #top-hud > div {
        padding: 6px 10px;
        font-size: 0.9rem;
        flex: 1;
        min-width: 80px;
        text-align: center;
    }

    /* Mobile Leaderboard */
    #leaderboard {
        top: 80px;
        right: 10px;
        left: 10px;
        max-height: 150px;
        overflow-y: auto;
        font-size: 0.8rem;
        padding: 10px;
    }

    #leaderboard h3 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    #leaderboard-list li {
        padding: 3px 0;
        font-size: 0.8rem;
    }

    /* Mobile Settings */
    .settings-content {
        padding: 20px;
        margin: 10px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .settings-group {
        padding: 12px;
        margin-bottom: 15px;
    }

    .settings-group h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .settings-group label {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    /* Mobile Game Over */
    .game-over-content {
        padding: 20px;
        margin: 10px;
        max-width: 90vw;
    }

    .game-over-content h2 {
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    #final-stats {
        padding: 15px;
        margin: 15px 0;
    }

    #final-stats p {
        font-size: 1rem;
        margin: 6px 0;
    }

    /* Touch-friendly skin selector */
    .skin-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        padding: 10px;
    }

    .skin-option {
        width: 50px;
        height: 50px;
        border-width: 4px;
    }

    /* Menu toggle for mobile */
    #menu-toggle {
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        padding: 12px 18px;
        font-size: 1.1rem;
        z-index: 150;
    }

    /* Mobile game menu */
    .menu-panel {
        padding: 25px;
        margin: 10px;
        max-width: 90vw;
    }

    .menu-panel .menu-btn {
        padding: 15px 20px;
        margin: 8px 0;
        font-size: 1.1rem;
    }

    /* Player info mobile adjustments */
    .player-info {
        padding: 15px;
        margin-top: 15px;
    }

    #player-name {
        padding: 15px;
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="range"] {
        font-size: 16px;
    }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    #mobile-controls {
        height: 150px;
    }

    #movement-joystick {
        width: 100px;
        height: 100px;
        bottom: 20px;
        left: 20px;
    }

    #joystick-knob {
        width: 40px;
        height: 40px;
    }

    #action-buttons {
        bottom: 20px;
        right: 20px;
        gap: 10px;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        font-size: 0.8rem;
    }

    #top-hud {
        top: 5px;
        left: 5px;
        right: 5px;
    }

    #leaderboard {
        top: 50px;
        right: 5px;
        left: auto;
        width: 180px;
        max-height: 120px;
        font-size: 0.7rem;
    }

    #menu-toggle {
        top: 5px;
        padding: 8px 12px;
        font-size: 1rem;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .menu-content h1 {
        font-size: 2rem;
    }

    .menu-btn {
        padding: 15px 18px;
        font-size: 1.1rem;
    }

    #movement-joystick {
        width: 100px;
        height: 100px;
        bottom: 20px;
        left: 20px;
    }

    #joystick-knob {
        width: 40px;
        height: 40px;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        font-size: 0.8rem;
    }

    #top-hud > div {
        font-size: 0.8rem;
        padding: 4px 8px;
    }

    .skin-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .skin-option {
        width: 45px;
        height: 45px;
    }
}
