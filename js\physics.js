/**
 * Physics engine for collision detection and movement
 */

class Physics {
    constructor(worldWidth, worldHeight) {
        this.worldWidth = worldWidth;
        this.worldHeight = worldHeight;
        this.gravity = 0; // No gravity in agar.io
        this.friction = 0.95; // Slight friction for smooth movement
    }

    /**
     * Update entity physics
     */
    updateEntity(entity, deltaTime) {
        // Apply velocity
        entity.x += entity.vx * deltaTime;
        entity.y += entity.vy * deltaTime;

        // Apply friction
        entity.vx *= this.friction;
        entity.vy *= this.friction;

        // Keep entity within world bounds
        this.constrainToWorld(entity);
    }

    /**
     * Constrain entity to world boundaries
     */
    constrainToWorld(entity) {
        const radius = entity.radius || 10;
        
        if (entity.x - radius < 0) {
            entity.x = radius;
            entity.vx = 0;
        } else if (entity.x + radius > this.worldWidth) {
            entity.x = this.worldWidth - radius;
            entity.vx = 0;
        }

        if (entity.y - radius < 0) {
            entity.y = radius;
            entity.vy = 0;
        } else if (entity.y + radius > this.worldHeight) {
            entity.y = this.worldHeight - radius;
            entity.vy = 0;
        }
    }

    /**
     * Check collision between two circular entities
     */
    checkCollision(entity1, entity2) {
        const dx = entity2.x - entity1.x;
        const dy = entity2.y - entity1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const minDistance = entity1.radius + entity2.radius;

        return {
            colliding: distance < minDistance,
            distance: distance,
            overlap: minDistance - distance,
            dx: dx,
            dy: dy
        };
    }

    /**
     * Check if one entity can eat another
     */
    canEat(predator, prey) {
        // Predator must be significantly larger to eat prey
        const massRatio = predator.mass / prey.mass;
        return massRatio >= 1.2; // 20% larger minimum
    }

    /**
     * Handle eating collision
     */
    handleEating(predator, prey) {
        if (!this.canEat(predator, prey)) return false;

        const collision = this.checkCollision(predator, prey);
        if (!collision.colliding) return false;

        // Transfer mass
        predator.mass += prey.mass * 0.8; // 80% efficiency
        predator.radius = Utils.radiusFromMass(predator.mass);
        
        // Update predator's speed based on new mass
        predator.maxSpeed = Utils.speedFromMass(predator.mass);

        return true;
    }

    /**
     * Apply force to entity
     */
    applyForce(entity, forceX, forceY) {
        const mass = entity.mass || 1;
        entity.vx += forceX / mass;
        entity.vy += forceY / mass;
    }

    /**
     * Move entity towards target
     */
    moveTowards(entity, targetX, targetY, force = 1) {
        const dx = targetX - entity.x;
        const dy = targetY - entity.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            const normalizedX = dx / distance;
            const normalizedY = dy / distance;
            
            this.applyForce(entity, normalizedX * force, normalizedY * force);
        }
    }

    /**
     * Apply repulsion force between entities
     */
    applyRepulsion(entity1, entity2, force = 1) {
        const dx = entity1.x - entity2.x;
        const dy = entity1.y - entity2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0 && distance < 100) { // Only apply if close
            const normalizedX = dx / distance;
            const normalizedY = dy / distance;
            const repulsionForce = force / (distance * distance);
            
            this.applyForce(entity1, normalizedX * repulsionForce, normalizedY * repulsionForce);
            this.applyForce(entity2, -normalizedX * repulsionForce, -normalizedY * repulsionForce);
        }
    }

    /**
     * Handle cell splitting physics
     */
    splitCell(cell, direction) {
        if (cell.mass < 20) return null; // Minimum mass to split

        const newMass = cell.mass / 2;
        const newRadius = Utils.radiusFromMass(newMass);
        
        // Update original cell
        cell.mass = newMass;
        cell.radius = newRadius;
        cell.maxSpeed = Utils.speedFromMass(newMass);

        // Create new cell
        const splitDistance = (cell.radius + newRadius) * 1.5;
        const newCell = {
            x: cell.x + Math.cos(direction) * splitDistance,
            y: cell.y + Math.sin(direction) * splitDistance,
            vx: Math.cos(direction) * 15, // Initial split velocity
            vy: Math.sin(direction) * 15,
            radius: newRadius,
            mass: newMass,
            maxSpeed: Utils.speedFromMass(newMass),
            color: cell.color,
            playerId: cell.playerId,
            type: cell.type || 'player'
        };

        // Ensure new cell is within bounds
        this.constrainToWorld(newCell);

        return newCell;
    }

    /**
     * Handle cell ejection (mass ejection)
     */
    ejectMass(cell, direction) {
        if (cell.mass < 15) return null; // Minimum mass to eject

        const ejectMass = Math.min(cell.mass * 0.1, 5); // Eject 10% or max 5 mass
        cell.mass -= ejectMass;
        cell.radius = Utils.radiusFromMass(cell.mass);
        cell.maxSpeed = Utils.speedFromMass(cell.mass);

        // Create ejected mass
        const ejectDistance = cell.radius + 15;
        const ejectedMass = {
            x: cell.x + Math.cos(direction) * ejectDistance,
            y: cell.y + Math.sin(direction) * ejectDistance,
            vx: Math.cos(direction) * 20, // Fast initial velocity
            vy: Math.sin(direction) * 20,
            radius: Utils.radiusFromMass(ejectMass),
            mass: ejectMass,
            color: cell.color,
            type: 'ejected',
            lifetime: 5000, // 5 seconds before becoming food
            createdAt: Date.now()
        };

        return ejectedMass;
    }

    /**
     * Check if cells can merge
     */
    canMerge(cell1, cell2) {
        // Cells can merge if they belong to the same player and enough time has passed
        if (cell1.playerId !== cell2.playerId) return false;
        if (!cell1.splitTime || !cell2.splitTime) return true;
        
        const mergeDelay = 15000; // 15 seconds
        const now = Date.now();
        return (now - cell1.splitTime > mergeDelay) && (now - cell2.splitTime > mergeDelay);
    }

    /**
     * Merge two cells
     */
    mergeCells(cell1, cell2) {
        if (!this.canMerge(cell1, cell2)) return null;

        const collision = this.checkCollision(cell1, cell2);
        if (!collision.colliding) return null;

        // Create merged cell
        const totalMass = cell1.mass + cell2.mass;
        const mergedCell = {
            x: (cell1.x * cell1.mass + cell2.x * cell2.mass) / totalMass,
            y: (cell1.y * cell1.mass + cell2.y * cell2.mass) / totalMass,
            vx: (cell1.vx * cell1.mass + cell2.vx * cell2.mass) / totalMass,
            vy: (cell1.vy * cell1.mass + cell2.vy * cell2.mass) / totalMass,
            mass: totalMass,
            radius: Utils.radiusFromMass(totalMass),
            maxSpeed: Utils.speedFromMass(totalMass),
            color: cell1.color,
            playerId: cell1.playerId,
            type: cell1.type
        };

        return mergedCell;
    }

    /**
     * Update ejected mass
     */
    updateEjectedMass(ejectedMass, deltaTime) {
        this.updateEntity(ejectedMass, deltaTime);

        // Check if it should become food
        if (Date.now() - ejectedMass.createdAt > ejectedMass.lifetime) {
            ejectedMass.type = 'food';
            ejectedMass.color = Utils.randomColor();
            delete ejectedMass.lifetime;
            delete ejectedMass.createdAt;
        }
    }

    /**
     * Spatial partitioning for efficient collision detection
     */
    createSpatialGrid(entities, cellSize = 100) {
        const grid = new Map();
        
        for (const entity of entities) {
            const gridX = Math.floor(entity.x / cellSize);
            const gridY = Math.floor(entity.y / cellSize);
            const key = `${gridX},${gridY}`;
            
            if (!grid.has(key)) {
                grid.set(key, []);
            }
            grid.get(key).push(entity);
        }
        
        return grid;
    }

    /**
     * Get nearby entities using spatial grid
     */
    getNearbyEntities(entity, grid, cellSize = 100, range = 1) {
        const nearby = [];
        const centerX = Math.floor(entity.x / cellSize);
        const centerY = Math.floor(entity.y / cellSize);
        
        for (let x = centerX - range; x <= centerX + range; x++) {
            for (let y = centerY - range; y <= centerY + range; y++) {
                const key = `${x},${y}`;
                if (grid.has(key)) {
                    nearby.push(...grid.get(key));
                }
            }
        }
        
        return nearby.filter(e => e !== entity);
    }

    /**
     * Limit entity velocity
     */
    limitVelocity(entity) {
        const maxSpeed = entity.maxSpeed || 5;
        const currentSpeed = Math.sqrt(entity.vx * entity.vx + entity.vy * entity.vy);
        
        if (currentSpeed > maxSpeed) {
            const ratio = maxSpeed / currentSpeed;
            entity.vx *= ratio;
            entity.vy *= ratio;
        }
    }
}

// Export for use in other modules
window.Physics = Physics;
