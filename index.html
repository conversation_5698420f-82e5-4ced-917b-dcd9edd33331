<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agar.io Remake</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/mobile.css">
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">
    <meta name="description" content="A comprehensive agar.io remake with enhanced features">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="screen">
        <div class="loading-content">
            <h1>Agar.io Remake</h1>
            <div class="loading-spinner"></div>
            <p>Loading game...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="main-menu" class="screen hidden">
        <div class="menu-content">
            <h1>Agar.io Remake</h1>
            <div class="menu-buttons">
                <button type="button" id="play-single" class="menu-btn primary">Single Player</button>
                <button type="button" id="play-multi" class="menu-btn">Multiplayer</button>
                <button type="button" id="settings-btn" class="menu-btn">Settings</button>
                <button type="button" id="leaderboard-btn" class="menu-btn">Leaderboard</button>
            </div>
            <div class="player-info">
                <input type="text" id="player-name" placeholder="Enter your name" maxlength="15">
                <div class="skin-selector">
                    <label>Choose skin:</label>
                    <div id="skin-options" class="skin-grid">
                        <!-- Skin options will be populated by JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Screen -->
    <div id="game-screen" class="screen hidden">
        <!-- Game Canvas -->
        <canvas id="game-canvas"></canvas>

        <!-- Game UI Overlay -->
        <div id="game-ui">
            <!-- Top HUD -->
            <div id="top-hud">
                <div id="score-display">Score: <span id="score">0</span></div>
                <div id="mass-display">Mass: <span id="mass">10</span></div>
                <div id="fps-counter">FPS: <span id="fps">60</span></div>
            </div>

            <!-- Leaderboard -->
            <div id="leaderboard">
                <h3>Leaderboard</h3>
                <ol id="leaderboard-list">
                    <!-- Populated by JS -->
                </ol>
            </div>

            <!-- Mobile Controls -->
            <div id="mobile-controls" class="mobile-only">
                <div id="movement-joystick">
                    <div id="joystick-knob"></div>
                </div>
                <div id="action-buttons">
                    <button type="button" id="split-btn" class="action-btn">Split</button>
                    <button type="button" id="eject-btn" class="action-btn">Eject</button>
                </div>
            </div>

            <!-- Game Menu Button -->
            <button type="button" id="menu-toggle" class="ui-btn">☰</button>
        </div>

        <!-- In-Game Menu -->
        <div id="game-menu" class="overlay hidden">
            <div class="menu-panel">
                <h3>Game Menu</h3>
                <button type="button" id="resume-btn" class="menu-btn">Resume</button>
                <button type="button" id="restart-btn" class="menu-btn">Restart</button>
                <button type="button" id="main-menu-btn" class="menu-btn">Main Menu</button>
            </div>
        </div>
    </div>

    <!-- Settings Screen -->
    <div id="settings-screen" class="screen hidden">
        <div class="settings-content">
            <h2>Settings</h2>
            <div class="settings-group">
                <h3>Graphics</h3>
                <label>
                    <input type="checkbox" id="show-grid" checked> Show Grid
                </label>
                <label>
                    <input type="checkbox" id="show-names" checked> Show Names
                </label>
                <label>
                    <input type="checkbox" id="smooth-rendering" checked> Smooth Rendering
                </label>
            </div>
            <div class="settings-group">
                <h3>Audio</h3>
                <label>
                    Master Volume: <input type="range" id="master-volume" min="0" max="100" value="50">
                </label>
                <label>
                    <input type="checkbox" id="sound-effects" checked> Sound Effects
                </label>
            </div>
            <div class="settings-group">
                <h3>Controls</h3>
                <label>
                    Mouse Sensitivity: <input type="range" id="mouse-sensitivity" min="0.1" max="2" step="0.1" value="1">
                </label>
            </div>
            <button type="button" id="settings-back" class="menu-btn">Back</button>
        </div>
    </div>

    <!-- Game Over Screen -->
    <div id="game-over-screen" class="overlay hidden">
        <div class="game-over-content">
            <h2>Game Over</h2>
            <div id="final-stats">
                <p>Final Score: <span id="final-score">0</span></p>
                <p>Max Mass: <span id="max-mass">0</span></p>
                <p>Time Played: <span id="time-played">0:00</span></p>
                <p>Cells Eaten: <span id="cells-eaten">0</span></p>
            </div>
            <div class="game-over-buttons">
                <button type="button" id="play-again-btn" class="menu-btn primary">Play Again</button>
                <button type="button" id="back-to-menu-btn" class="menu-btn">Main Menu</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/food.js"></script>
    <script src="js/player.js"></script>
    <script src="js/bot.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/input.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/game.js"></script>
    <script>
        // Initialize the game when page loads
        window.addEventListener('load', () => {
            const game = new Game();
            game.init();
        });
    </script>
</body>
</html>
