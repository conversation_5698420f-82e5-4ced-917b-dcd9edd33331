/**
 * Node.js server for Agar.io remake multiplayer functionality
 * This is a placeholder for Phase 2 implementation
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Serve the main game file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index.html'));
});

// Game state (placeholder for multiplayer)
const gameRooms = new Map();
const players = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log(`Player connected: ${socket.id}`);
    
    // Handle player joining
    socket.on('join-game', (playerData) => {
        console.log(`Player ${playerData.name} joined with ID: ${socket.id}`);
        
        // Store player data
        players.set(socket.id, {
            id: socket.id,
            name: playerData.name,
            color: playerData.color,
            x: Math.random() * 3000,
            y: Math.random() * 3000,
            mass: 10,
            score: 0,
            alive: true
        });
        
        // Send initial game state
        socket.emit('game-state', {
            players: Array.from(players.values()),
            worldWidth: 3000,
            worldHeight: 3000
        });
        
        // Notify other players
        socket.broadcast.emit('player-joined', players.get(socket.id));
    });
    
    // Handle player movement
    socket.on('player-move', (moveData) => {
        const player = players.get(socket.id);
        if (player && player.alive) {
            player.targetX = moveData.x;
            player.targetY = moveData.y;
            
            // Broadcast movement to other players
            socket.broadcast.emit('player-moved', {
                id: socket.id,
                x: moveData.x,
                y: moveData.y
            });
        }
    });
    
    // Handle player split
    socket.on('player-split', (splitData) => {
        const player = players.get(socket.id);
        if (player && player.alive && player.mass >= 20) {
            console.log(`Player ${player.name} split`);
            
            // Broadcast split to other players
            socket.broadcast.emit('player-split', {
                id: socket.id,
                direction: splitData.direction
            });
        }
    });
    
    // Handle player eject
    socket.on('player-eject', (ejectData) => {
        const player = players.get(socket.id);
        if (player && player.alive && player.mass >= 15) {
            console.log(`Player ${player.name} ejected mass`);
            
            // Broadcast eject to other players
            socket.broadcast.emit('player-ejected', {
                id: socket.id,
                direction: ejectData.direction
            });
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
        console.log(`Player disconnected: ${socket.id}`);
        
        // Remove player
        players.delete(socket.id);
        
        // Notify other players
        socket.broadcast.emit('player-left', socket.id);
    });
    
    // Handle ping for latency measurement
    socket.on('ping', (timestamp) => {
        socket.emit('pong', timestamp);
    });
});

// API endpoints
app.get('/api/status', (req, res) => {
    res.json({
        status: 'online',
        players: players.size,
        rooms: gameRooms.size,
        uptime: process.uptime()
    });
});

app.get('/api/leaderboard', (req, res) => {
    const leaderboard = Array.from(players.values())
        .filter(player => player.alive)
        .sort((a, b) => b.score - a.score)
        .slice(0, 10)
        .map(player => ({
            name: player.name,
            score: player.score
        }));
    
    res.json(leaderboard);
});

// Error handling
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
server.listen(PORT, () => {
    console.log(`🎮 Agar.io Remake Server running on port ${PORT}`);
    console.log(`🌐 Game available at: http://localhost:${PORT}`);
    console.log(`📊 API status: http://localhost:${PORT}/api/status`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Server shutting down...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

module.exports = { app, server, io };
