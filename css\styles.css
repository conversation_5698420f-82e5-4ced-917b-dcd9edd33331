/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    overflow: hidden;
    -webkit-user-select: none;
    user-select: none;
}

/* Screen Management */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
}

.screen.hidden {
    display: none;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.overlay.hidden {
    display: none;
}

/* Loading Screen */
#loading-screen {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.loading-content {
    text-align: center;
}

.loading-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    color: #ecf0f1;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #34495e;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Main Menu */
.menu-content {
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.menu-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.menu-buttons {
    margin-bottom: 2rem;
}

.menu-btn {
    display: block;
    width: 100%;
    padding: 15px 20px;
    margin: 10px 0;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.menu-btn.primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.menu-btn.primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
}

/* Player Info */
.player-info {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

#player-name {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
    margin-bottom: 15px;
}

.skin-selector label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.skin-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
}

.skin-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skin-option:hover,
.skin-option.selected {
    border-color: #3498db;
    transform: scale(1.1);
}

/* Game Screen */
#game-screen {
    padding: 0;
    background: #000;
}

#game-canvas {
    display: block;
    background: #1a1a1a;
}

/* Game UI */
#game-ui {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 100;
}

#game-ui > * {
    pointer-events: auto;
}

/* Top HUD */
#top-hud {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 20px;
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

#top-hud > div {
    background: rgba(0, 0, 0, 0.5);
    padding: 8px 12px;
    border-radius: 6px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

/* Leaderboard */
#leaderboard {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 10px;
    min-width: 200px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

#leaderboard h3 {
    margin-bottom: 10px;
    text-align: center;
    color: #3498db;
}

#leaderboard-list {
    list-style: none;
    font-size: 0.9rem;
}

#leaderboard-list li {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
}

#leaderboard-list li:last-child {
    border-bottom: none;
}

.leaderboard-player {
    color: #ecf0f1;
}

.leaderboard-score {
    color: #f39c12;
    font-weight: bold;
}

/* Menu Toggle */
#menu-toggle {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.2rem;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

/* Game Menu Panel */
.menu-panel {
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-panel h3 {
    margin-bottom: 20px;
    color: #3498db;
    font-size: 1.5rem;
}

/* Settings Screen */
.settings-content {
    background: rgba(0, 0, 0, 0.8);
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.settings-content h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #3498db;
}

.settings-group {
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.settings-group h3 {
    margin-bottom: 15px;
    color: #ecf0f1;
}

.settings-group label {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
}

.settings-group input[type="range"] {
    width: 100%;
    margin-left: 10px;
}

/* Game Over Screen */
.game-over-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-over-content h2 {
    color: #e74c3c;
    margin-bottom: 20px;
    font-size: 2rem;
}

#final-stats {
    margin: 20px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

#final-stats p {
    margin: 8px 0;
    font-size: 1.1rem;
}

.game-over-buttons {
    margin-top: 20px;
}

/* Utility Classes */
.ui-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.ui-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.mobile-only {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-content h1 {
        font-size: 2rem;
    }
    
    #top-hud {
        flex-direction: column;
        gap: 10px;
    }
    
    #leaderboard {
        position: relative;
        margin: 10px;
        width: calc(100% - 20px);
    }
}
