/**
 * Utility functions for the Agar.io remake
 */

class Utils {
    /**
     * Generate a random number between min and max (inclusive)
     */
    static random(min, max) {
        return Math.random() * (max - min) + min;
    }

    /**
     * Generate a random integer between min and max (inclusive)
     */
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * Calculate distance between two points
     */
    static distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Calculate angle between two points
     */
    static angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    }

    /**
     * Normalize angle to 0-2π range
     */
    static normalizeAngle(angle) {
        while (angle < 0) angle += Math.PI * 2;
        while (angle >= Math.PI * 2) angle -= Math.PI * 2;
        return angle;
    }

    /**
     * Linear interpolation
     */
    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }

    /**
     * Clamp value between min and max
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    /**
     * Check if two circles collide
     */
    static circleCollision(x1, y1, r1, x2, y2, r2) {
        const distance = this.distance(x1, y1, x2, y2);
        return distance < (r1 + r2);
    }

    /**
     * Check if a point is inside a circle
     */
    static pointInCircle(px, py, cx, cy, radius) {
        return this.distance(px, py, cx, cy) <= radius;
    }

    /**
     * Generate a random color
     */
    static randomColor() {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        return colors[this.randomInt(0, colors.length - 1)];
    }

    /**
     * Generate a random name for bots
     */
    static randomBotName() {
        const names = [
            'Bot Alpha', 'Bot Beta', 'Bot Gamma', 'Bot Delta', 'Bot Epsilon',
            'Cyber Cell', 'Nano Bot', 'Micro Mind', 'Data Blob', 'Code Cell',
            'AI Entity', 'Neural Net', 'Logic Loop', 'Byte Beast', 'Pixel Pod',
            'Digital Dot', 'Virtual Void', 'Quantum Cell', 'Binary Blob', 'Tech Sphere'
        ];
        return names[this.randomInt(0, names.length - 1)];
    }

    /**
     * Format time in MM:SS format
     */
    static formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * Format large numbers with K, M suffixes
     */
    static formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * Debounce function calls
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function calls
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Check if device is mobile
     */
    static isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * Check if device supports touch
     */
    static isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * Get viewport dimensions
     */
    static getViewport() {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }

    /**
     * Save data to localStorage
     */
    static saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (e) {
            console.warn('Failed to save to localStorage:', e);
            return false;
        }
    }

    /**
     * Load data from localStorage
     */
    static loadFromStorage(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.warn('Failed to load from localStorage:', e);
            return defaultValue;
        }
    }

    /**
     * Generate unique ID
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Calculate mass from radius (area-based)
     */
    static massFromRadius(radius) {
        return Math.PI * radius * radius / 100; // Scaled down for gameplay
    }

    /**
     * Calculate radius from mass
     */
    static radiusFromMass(mass) {
        return Math.sqrt(mass * 100 / Math.PI);
    }

    /**
     * Calculate speed based on mass (larger = slower)
     */
    static speedFromMass(mass) {
        return Math.max(1, 5 - Math.log(mass + 1) * 0.5);
    }

    /**
     * Check if two rectangles overlap
     */
    static rectOverlap(x1, y1, w1, h1, x2, y2, w2, h2) {
        return x1 < x2 + w2 && x1 + w1 > x2 && y1 < y2 + h2 && y1 + h1 > y2;
    }

    /**
     * Get random position within bounds
     */
    static randomPosition(minX, minY, maxX, maxY) {
        return {
            x: this.random(minX, maxX),
            y: this.random(minY, maxY)
        };
    }

    /**
     * Validate player name
     */
    static validatePlayerName(name) {
        if (!name || typeof name !== 'string') return false;
        name = name.trim();
        if (name.length < 1 || name.length > 15) return false;
        // Check for inappropriate content (basic filter)
        const inappropriate = ['fuck', 'shit', 'damn', 'bitch', 'ass'];
        const lowerName = name.toLowerCase();
        return !inappropriate.some(word => lowerName.includes(word));
    }

    /**
     * Sanitize player name
     */
    static sanitizePlayerName(name) {
        if (!name || typeof name !== 'string') return 'Anonymous';
        name = name.trim().substring(0, 15);
        return this.validatePlayerName(name) ? name : 'Anonymous';
    }

    /**
     * Calculate viewport bounds for camera
     */
    static calculateViewBounds(centerX, centerY, zoom, viewWidth, viewHeight) {
        const halfWidth = (viewWidth / zoom) / 2;
        const halfHeight = (viewHeight / zoom) / 2;
        
        return {
            left: centerX - halfWidth,
            right: centerX + halfWidth,
            top: centerY - halfHeight,
            bottom: centerY + halfHeight
        };
    }

    /**
     * Check if object is visible in viewport
     */
    static isVisible(x, y, radius, viewBounds) {
        return x + radius >= viewBounds.left &&
               x - radius <= viewBounds.right &&
               y + radius >= viewBounds.top &&
               y - radius <= viewBounds.bottom;
    }
}

// Export for use in other modules
window.Utils = Utils;
