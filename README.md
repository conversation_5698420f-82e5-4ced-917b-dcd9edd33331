# Agar.io Remake

A comprehensive agar.io remake built with vanilla HTML5, CSS3, and JavaScript. This progressive web application features enhanced gameplay mechanics, AI bots, and mobile support.

## Features

### Phase 1 - Core Implementation ✅
- ✅ Basic game loop with HTML5 Canvas rendering
- ✅ Single-player mode with AI bots (5 difficulty levels)
- ✅ Core mechanics: cell movement, growth, splitting, merging
- ✅ Collision detection and physics engine
- ✅ Responsive UI with score display and leaderboard
- ✅ Mobile-responsive controls (touch/joystick)

### Phase 2 - Multiplayer Foundation (Coming Soon)
- 🔄 WebSocket server implementation
- 🔄 Room-based game sessions
- 🔄 Real-time synchronization
- 🔄 Basic anti-cheat validation

### Phase 3 - Enhanced Features (Planned)
- 📋 Multiple game modes
- 📋 Cell customization and skins
- 📋 Power-ups system
- 📋 Progression and achievements

## Technology Stack

- **Frontend**: Vanilla HTML5, CSS3, JavaScript (ES6+)
- **Graphics**: HTML5 Canvas with 60 FPS rendering
- **Mobile**: Touch controls with virtual joystick
- **PWA**: Progressive Web App with offline support
- **Storage**: LocalStorage for settings and statistics

## Quick Start

### Option 1: Direct Browser (Recommended)
1. Clone or download this repository
2. Open `index.html` in a modern web browser
3. Start playing immediately!

### Option 2: Local Server
```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (if you have it)
npx serve .

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## Game Controls

### Desktop
- **Mouse**: Move your cell
- **Space**: Split cell
- **W**: Eject mass
- **Escape**: Open game menu

### Mobile
- **Virtual Joystick**: Move your cell
- **Split Button**: Split cell
- **Eject Button**: Eject mass
- **Tap**: Quick split

## Game Mechanics

### Basic Gameplay
- Eat food pellets to grow larger
- Eat smaller players to gain mass
- Avoid larger players that can eat you
- Split to move faster or catch prey
- Eject mass to escape or feed allies

### AI Bot Difficulties
1. **Easy**: Slow reaction, basic behavior
2. **Medium**: Balanced gameplay
3. **Hard**: Fast reactions, strategic play
4. **Expert**: Advanced tactics
5. **Insane**: Near-perfect play

### Physics System
- Mass-based movement speed
- Realistic collision detection
- Smooth interpolation for 60 FPS
- Cell splitting and merging mechanics

## File Structure

```
agar.io-remake/
├── index.html              # Main game page
├── manifest.json           # PWA manifest
├── css/
│   ├── styles.css         # Main styles
│   └── mobile.css         # Mobile-specific styles
├── js/
│   ├── game.js            # Main game class
│   ├── player.js          # Player cell class
│   ├── bot.js             # AI bot implementation
│   ├── food.js            # Food system
│   ├── physics.js         # Physics engine
│   ├── renderer.js        # Canvas rendering
│   ├── input.js           # Input handling
│   ├── ui.js              # UI management
│   └── utils.js           # Utility functions
└── README.md              # This file
```

## Development

### Code Architecture
The game follows a modular architecture with separate systems:

- **Game**: Main game loop and state management
- **Physics**: Collision detection and movement
- **Renderer**: Canvas-based graphics rendering
- **Input**: Mouse, keyboard, and touch handling
- **UI**: Menu system and HUD management
- **Player/Bot**: Entity classes with AI behaviors

### Performance Optimizations
- Spatial partitioning for collision detection
- Viewport culling for rendering
- Object pooling for food pellets
- Smooth interpolation for animations
- Mobile-optimized touch controls

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+
- Mobile browsers with Canvas support

## Customization

### Game Settings
Modify these values in `js/game.js`:
```javascript
this.worldWidth = 3000;      // World size
this.worldHeight = 3000;
this.maxBots = 15;           // Number of AI bots
```

### Visual Settings
Customize appearance in `css/styles.css`:
```css
:root {
  --primary-color: #3498db;
  --background-color: #1a1a1a;
  --grid-color: #333;
}
```

### Bot Difficulty
Adjust AI parameters in `js/bot.js`:
```javascript
// Example: Make bots more aggressive
this.aggressiveness = 0.8;
this.reactionTime = 200;
```

## Performance Tips

### For Smooth Gameplay
- Use a modern browser with hardware acceleration
- Close unnecessary tabs and applications
- Ensure stable internet connection (for future multiplayer)
- Lower graphics settings on older devices

### Mobile Optimization
- The game automatically adjusts quality on mobile
- Touch controls are optimized for different screen sizes
- Battery usage is minimized through efficient rendering

## Troubleshooting

### Common Issues
1. **Game won't load**: Ensure JavaScript is enabled
2. **Poor performance**: Try lowering graphics settings
3. **Touch controls not working**: Ensure touch events are supported
4. **Canvas errors**: Update to a modern browser

### Debug Mode
Open browser console (F12) to see debug information and error messages.

## Contributing

This is a learning project, but contributions are welcome! Areas for improvement:
- Multiplayer implementation
- Additional game modes
- Enhanced graphics and effects
- Sound system
- More sophisticated AI

## License

This project is open source and available under the MIT License.

## Acknowledgments

- Inspired by the original Agar.io game
- Built with modern web technologies
- Designed for educational purposes

---

**Enjoy playing!** 🎮

For questions or issues, please check the browser console for error messages.
