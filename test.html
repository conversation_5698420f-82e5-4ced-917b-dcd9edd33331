<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agar.io Remake - Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #test-canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎮 Agar.io Remake - Test Suite</h1>
    
    <div class="test-section">
        <h2>📋 Test Controls</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        <button onclick="window.open('index.html', '_blank')">Open Game</button>
    </div>

    <div class="test-section">
        <h2>🧪 Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>🎯 Manual Tests</h2>
        <canvas id="test-canvas" width="400" height="300"></canvas>
        <div>
            <button onclick="testRendering()">Test Rendering</button>
            <button onclick="testPhysics()">Test Physics</button>
            <button onclick="testInput()">Test Input</button>
        </div>
    </div>

    <!-- Include game scripts for testing -->
    <script src="js/utils.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/food.js"></script>
    <script src="js/player.js"></script>
    <script src="js/bot.js"></script>
    <script src="js/renderer.js"></script>

    <script>
        let testResults = [];

        function addTestResult(name, passed, message = '') {
            testResults.push({ name, passed, message });
            updateTestDisplay();
        }

        function updateTestDisplay() {
            const container = document.getElementById('test-results');
            container.innerHTML = '';
            
            let passCount = 0;
            let totalCount = testResults.length;
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'test-pass' : 'test-fail'}`;
                div.innerHTML = `
                    <strong>${result.passed ? '✅' : '❌'} ${result.name}</strong>
                    ${result.message ? `<br><small>${result.message}</small>` : ''}
                `;
                container.appendChild(div);
                
                if (result.passed) passCount++;
            });
            
            if (totalCount > 0) {
                const summary = document.createElement('div');
                summary.className = 'test-result test-info';
                summary.innerHTML = `<strong>Summary: ${passCount}/${totalCount} tests passed (${Math.round(passCount/totalCount*100)}%)</strong>`;
                container.insertBefore(summary, container.firstChild);
            }
        }

        function clearResults() {
            testResults = [];
            updateTestDisplay();
        }

        function runAllTests() {
            clearResults();
            
            // Test 1: Utils functions
            try {
                const distance = Utils.distance(0, 0, 3, 4);
                addTestResult('Utils.distance', Math.abs(distance - 5) < 0.001, `Expected 5, got ${distance}`);
            } catch (e) {
                addTestResult('Utils.distance', false, e.message);
            }

            // Test 2: Random functions
            try {
                const random = Utils.random(1, 10);
                addTestResult('Utils.random', random >= 1 && random <= 10, `Generated ${random}`);
            } catch (e) {
                addTestResult('Utils.random', false, e.message);
            }

            // Test 3: Mass/Radius conversion
            try {
                const mass = 100;
                const radius = Utils.radiusFromMass(mass);
                const backToMass = Utils.massFromRadius(radius);
                addTestResult('Mass/Radius conversion', Math.abs(mass - backToMass) < 0.1, 
                    `${mass} -> ${radius} -> ${backToMass}`);
            } catch (e) {
                addTestResult('Mass/Radius conversion', false, e.message);
            }

            // Test 4: Physics engine
            try {
                const physics = new Physics(1000, 1000);
                addTestResult('Physics initialization', physics instanceof Physics, 'Physics engine created');
            } catch (e) {
                addTestResult('Physics initialization', false, e.message);
            }

            // Test 5: Player creation
            try {
                const player = new Player(100, 100, 'TestPlayer');
                addTestResult('Player creation', player.name === 'TestPlayer' && player.x === 100, 
                    `Player: ${player.name} at (${player.x}, ${player.y})`);
            } catch (e) {
                addTestResult('Player creation', false, e.message);
            }

            // Test 6: Bot creation
            try {
                const bot = new Bot(200, 200, 'medium');
                addTestResult('Bot creation', bot.difficulty === 'medium' && bot.type === 'bot', 
                    `Bot difficulty: ${bot.difficulty}`);
            } catch (e) {
                addTestResult('Bot creation', false, e.message);
            }

            // Test 7: Food manager
            try {
                const foodManager = new FoodManager(1000, 1000, 100);
                foodManager.init();
                addTestResult('Food manager', foodManager.getCount() > 0, 
                    `Generated ${foodManager.getCount()} food pellets`);
            } catch (e) {
                addTestResult('Food manager', false, e.message);
            }

            // Test 8: Collision detection
            try {
                const physics = new Physics(1000, 1000);
                const entity1 = { x: 0, y: 0, radius: 10 };
                const entity2 = { x: 5, y: 0, radius: 10 };
                const collision = physics.checkCollision(entity1, entity2);
                addTestResult('Collision detection', collision.colliding === true, 
                    `Collision detected: ${collision.colliding}`);
            } catch (e) {
                addTestResult('Collision detection', false, e.message);
            }

            // Test 9: Canvas support
            try {
                const canvas = document.getElementById('test-canvas');
                const ctx = canvas.getContext('2d');
                addTestResult('Canvas support', ctx !== null, 'Canvas 2D context available');
            } catch (e) {
                addTestResult('Canvas support', false, e.message);
            }

            // Test 10: Local storage
            try {
                Utils.saveToStorage('test', { value: 123 });
                const loaded = Utils.loadFromStorage('test');
                addTestResult('Local storage', loaded && loaded.value === 123, 
                    `Saved and loaded: ${JSON.stringify(loaded)}`);
            } catch (e) {
                addTestResult('Local storage', false, e.message);
            }
        }

        function testRendering() {
            const canvas = document.getElementById('test-canvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw test elements
            ctx.fillStyle = '#FF6B6B';
            ctx.beginPath();
            ctx.arc(100, 100, 30, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#4ECDC4';
            ctx.beginPath();
            ctx.arc(200, 150, 20, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#45B7D1';
            ctx.beginPath();
            ctx.arc(300, 100, 25, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw grid
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            for (let x = 0; x < canvas.width; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += 50) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            addTestResult('Manual Rendering Test', true, 'Check canvas for rendered circles and grid');
        }

        function testPhysics() {
            const physics = new Physics(1000, 1000);
            const player = new Player(100, 100, 'TestPlayer');
            
            // Test movement
            player.setTarget(200, 200);
            physics.updateEntity(player, 16); // Simulate 16ms frame
            
            const moved = player.x !== 100 || player.y !== 100;
            addTestResult('Physics Movement Test', moved, 
                `Player moved from (100,100) to (${player.x.toFixed(1)}, ${player.y.toFixed(1)})`);
        }

        function testInput() {
            // Test mouse position calculation
            const canvas = document.getElementById('test-canvas');
            const rect = canvas.getBoundingClientRect();
            
            canvas.addEventListener('click', function testClick(e) {
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                addTestResult('Input Test', true, 
                    `Mouse click detected at canvas position (${x.toFixed(1)}, ${y.toFixed(1)})`);
                canvas.removeEventListener('click', testClick);
            });
            
            addTestResult('Input Test Setup', true, 'Click on the canvas to test mouse input');
        }

        // Run basic tests on page load
        window.addEventListener('load', () => {
            addTestResult('Page Load', true, 'Test suite loaded successfully');
        });
    </script>
</body>
</html>
