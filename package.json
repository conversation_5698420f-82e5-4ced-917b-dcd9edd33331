{"name": "agar-io-remake", "version": "1.0.0", "description": "A comprehensive agar.io remake with enhanced features and gameplay mechanics", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "test": "echo \"No tests specified yet\" && exit 0", "build": "echo \"No build process needed for vanilla JS\" && exit 0", "serve": "python server.py"}, "keywords": ["agar.io", "game", "multiplayer", "canvas", "javascript", "html5", "pwa"], "author": "Agar.io Remake Team", "license": "MIT", "devDependencies": {"nodemon": "^3.0.1"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/agar-io-remake.git"}, "bugs": {"url": "https://github.com/your-username/agar-io-remake/issues"}, "homepage": "https://github.com/your-username/agar-io-remake#readme"}