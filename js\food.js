/**
 * Food pellet class for the Agar.io remake
 */

class Food {
    constructor(x, y, mass = 1) {
        this.x = x;
        this.y = y;
        this.mass = mass;
        this.radius = Utils.radiusFromMass(mass);
        this.color = Utils.randomColor();
        this.type = 'food';
        this.id = Utils.generateId();
        
        // Visual effects
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.pulseSpeed = 0.05;
        this.baseRadius = this.radius;
        
        // Animation properties
        this.spawnTime = Date.now();
        this.spawnAnimation = 0;
        this.spawnDuration = 300; // 300ms spawn animation
    }

    /**
     * Update food animation
     */
    update(deltaTime) {
        // Update pulse animation
        this.pulsePhase += this.pulseSpeed * deltaTime;
        this.radius = this.baseRadius + Math.sin(this.pulsePhase) * 0.5;

        // Update spawn animation
        const timeSinceSpawn = Date.now() - this.spawnTime;
        if (timeSinceSpawn < this.spawnDuration) {
            this.spawnAnimation = timeSinceSpawn / this.spawnDuration;
        } else {
            this.spawnAnimation = 1;
        }
    }

    /**
     * Render the food pellet
     */
    render(ctx, camera) {
        const screenX = (this.x - camera.x) * camera.zoom + camera.width / 2;
        const screenY = (this.y - camera.y) * camera.zoom + camera.height / 2;
        const screenRadius = this.radius * camera.zoom;

        // Skip rendering if outside viewport
        if (screenX + screenRadius < 0 || screenX - screenRadius > camera.width ||
            screenY + screenRadius < 0 || screenY - screenRadius > camera.height) {
            return;
        }

        ctx.save();

        // Apply spawn animation
        if (this.spawnAnimation < 1) {
            const scale = this.spawnAnimation;
            ctx.globalAlpha = this.spawnAnimation;
            ctx.translate(screenX, screenY);
            ctx.scale(scale, scale);
            ctx.translate(-screenX, -screenY);
        }

        // Draw food pellet
        ctx.beginPath();
        ctx.arc(screenX, screenY, screenRadius, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();

        // Add subtle glow effect
        if (camera.zoom > 0.5) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, screenRadius * 1.2, 0, Math.PI * 2);
            const gradient = ctx.createRadialGradient(
                screenX, screenY, 0,
                screenX, screenY, screenRadius * 1.2
            );
            gradient.addColorStop(0, this.color + '40');
            gradient.addColorStop(1, this.color + '00');
            ctx.fillStyle = gradient;
            ctx.fill();
        }

        ctx.restore();
    }

    /**
     * Check if food is visible in camera view
     */
    isVisible(camera) {
        const margin = this.radius * 2;
        return Utils.isVisible(this.x, this.y, margin, {
            left: camera.x - camera.width / (2 * camera.zoom),
            right: camera.x + camera.width / (2 * camera.zoom),
            top: camera.y - camera.height / (2 * camera.zoom),
            bottom: camera.y + camera.height / (2 * camera.zoom)
        });
    }

    /**
     * Get food data for serialization
     */
    serialize() {
        return {
            id: this.id,
            x: this.x,
            y: this.y,
            mass: this.mass,
            radius: this.baseRadius,
            color: this.color,
            type: this.type
        };
    }

    /**
     * Create food from serialized data
     */
    static deserialize(data) {
        const food = new Food(data.x, data.y, data.mass);
        food.id = data.id;
        food.color = data.color;
        food.baseRadius = data.radius;
        food.radius = data.radius;
        return food;
    }
}

/**
 * Food manager class to handle food generation and management
 */
class FoodManager {
    constructor(worldWidth, worldHeight, maxFood = 1000) {
        this.worldWidth = worldWidth;
        this.worldHeight = worldHeight;
        this.maxFood = maxFood;
        this.foods = new Map();
        this.spawnRate = 2; // Foods per second
        this.lastSpawn = 0;
        
        // Food generation settings
        this.minMass = 0.5;
        this.maxMass = 2;
        this.spawnMargin = 50; // Margin from world edges
    }

    /**
     * Initialize food manager with initial food
     */
    init() {
        // Generate initial food
        for (let i = 0; i < this.maxFood * 0.8; i++) {
            this.spawnFood();
        }
    }

    /**
     * Update food manager
     */
    update(deltaTime) {
        // Update existing food
        for (const food of this.foods.values()) {
            food.update(deltaTime);
        }

        // Spawn new food if needed
        const now = Date.now();
        if (now - this.lastSpawn > 1000 / this.spawnRate && this.foods.size < this.maxFood) {
            this.spawnFood();
            this.lastSpawn = now;
        }
    }

    /**
     * Spawn a new food pellet
     */
    spawnFood() {
        const x = Utils.random(this.spawnMargin, this.worldWidth - this.spawnMargin);
        const y = Utils.random(this.spawnMargin, this.worldHeight - this.spawnMargin);
        const mass = Utils.random(this.minMass, this.maxMass);
        
        const food = new Food(x, y, mass);
        this.foods.set(food.id, food);
        
        return food;
    }

    /**
     * Spawn food at specific location
     */
    spawnFoodAt(x, y, mass = null) {
        if (mass === null) {
            mass = Utils.random(this.minMass, this.maxMass);
        }
        
        const food = new Food(x, y, mass);
        this.foods.set(food.id, food);
        
        return food;
    }

    /**
     * Remove food by ID
     */
    removeFood(id) {
        return this.foods.delete(id);
    }

    /**
     * Get food by ID
     */
    getFood(id) {
        return this.foods.get(id);
    }

    /**
     * Get all foods as array
     */
    getAllFoods() {
        return Array.from(this.foods.values());
    }

    /**
     * Get foods in specific area
     */
    getFoodsInArea(x, y, width, height) {
        const result = [];
        for (const food of this.foods.values()) {
            if (food.x >= x && food.x <= x + width &&
                food.y >= y && food.y <= y + height) {
                result.push(food);
            }
        }
        return result;
    }

    /**
     * Get foods visible in camera view
     */
    getVisibleFoods(camera) {
        const result = [];
        for (const food of this.foods.values()) {
            if (food.isVisible(camera)) {
                result.push(food);
            }
        }
        return result;
    }

    /**
     * Clear all food
     */
    clear() {
        this.foods.clear();
    }

    /**
     * Get food count
     */
    getCount() {
        return this.foods.size;
    }

    /**
     * Render all visible foods
     */
    render(ctx, camera) {
        const visibleFoods = this.getVisibleFoods(camera);
        
        // Sort by size for proper rendering order (smaller first)
        visibleFoods.sort((a, b) => a.radius - b.radius);
        
        for (const food of visibleFoods) {
            food.render(ctx, camera);
        }
    }

    /**
     * Get serialized food data
     */
    serialize() {
        const foodArray = [];
        for (const food of this.foods.values()) {
            foodArray.push(food.serialize());
        }
        return foodArray;
    }

    /**
     * Load food from serialized data
     */
    deserialize(foodData) {
        this.clear();
        for (const data of foodData) {
            const food = Food.deserialize(data);
            this.foods.set(food.id, food);
        }
    }

    /**
     * Spawn food cluster (for special events)
     */
    spawnCluster(centerX, centerY, count = 10, radius = 50) {
        const foods = [];
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const distance = Utils.random(0, radius);
            const x = centerX + Math.cos(angle) * distance;
            const y = centerY + Math.sin(angle) * distance;
            
            // Ensure within world bounds
            const clampedX = Utils.clamp(x, this.spawnMargin, this.worldWidth - this.spawnMargin);
            const clampedY = Utils.clamp(y, this.spawnMargin, this.worldHeight - this.spawnMargin);
            
            const food = this.spawnFoodAt(clampedX, clampedY);
            foods.push(food);
        }
        return foods;
    }
}

// Export for use in other modules
window.Food = Food;
window.FoodManager = FoodManager;
