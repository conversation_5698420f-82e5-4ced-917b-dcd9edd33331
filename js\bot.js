/**
 * AI Bot class for the Agar.io remake
 */

class <PERSON><PERSON> extends Player {
    constructor(x, y, difficulty = 'medium') {
        super(x, y, Utils.randomBotName(), Utils.randomColor());
        
        this.type = 'bot';
        this.difficulty = difficulty;
        
        // AI properties
        this.aiState = 'seeking'; // seeking, fleeing, hunting, idle
        this.target = null;
        this.lastStateChange = Date.now();
        this.stateChangeCooldown = 2000; // 2 seconds
        
        // AI parameters based on difficulty
        this.setDifficultyParameters();
        
        // Decision making
        this.decisionTimer = 0;
        this.decisionInterval = this.reactionTime;
        this.lastDecision = Date.now();
        
        // Memory system
        this.memory = {
            threats: [], // Larger players to avoid
            food: [], // Known food locations
            prey: [] // Smaller players to hunt
        };
        this.memoryDuration = 5000; // 5 seconds
        
        // Pathfinding
        this.path = [];
        this.pathIndex = 0;
        this.pathUpdateInterval = 1000;
        this.lastPathUpdate = 0;
    }

    /**
     * Set AI parameters based on difficulty
     */
    setDifficultyParameters() {
        switch (this.difficulty) {
            case 'easy':
                this.visionRange = 150;
                this.reactionTime = 800;
                this.aggressiveness = 0.3;
                this.splitChance = 0.1;
                this.ejectChance = 0.05;
                this.accuracy = 0.6;
                break;
            case 'medium':
                this.visionRange = 200;
                this.reactionTime = 500;
                this.aggressiveness = 0.5;
                this.splitChance = 0.2;
                this.ejectChance = 0.1;
                this.accuracy = 0.8;
                break;
            case 'hard':
                this.visionRange = 250;
                this.reactionTime = 300;
                this.aggressiveness = 0.7;
                this.splitChance = 0.3;
                this.ejectChance = 0.15;
                this.accuracy = 0.9;
                break;
            case 'expert':
                this.visionRange = 300;
                this.reactionTime = 200;
                this.aggressiveness = 0.9;
                this.splitChance = 0.4;
                this.ejectChance = 0.2;
                this.accuracy = 0.95;
                break;
            case 'insane':
                this.visionRange = 400;
                this.reactionTime = 100;
                this.aggressiveness = 1.0;
                this.splitChance = 0.5;
                this.ejectChance = 0.25;
                this.accuracy = 0.98;
                break;
        }
    }

    /**
     * Update bot AI
     */
    update(deltaTime, physics, entities) {
        if (!this.alive) return;

        // Update decision timer
        this.decisionTimer += deltaTime;

        // Make decisions at intervals based on reaction time
        if (this.decisionTimer >= this.decisionInterval) {
            this.makeDecision(entities);
            this.decisionTimer = 0;
        }

        // Update memory
        this.updateMemory();

        // Execute current behavior
        this.executeBehavior(deltaTime, physics, entities);

        // Call parent update
        super.update(deltaTime, physics);
    }

    /**
     * Make AI decision
     */
    makeDecision(entities) {
        const nearbyEntities = this.findNearbyEntities(entities);
        
        // Analyze threats and opportunities
        const threats = nearbyEntities.filter(e => this.isThreat(e));
        const prey = nearbyEntities.filter(e => this.isPrey(e));
        const food = nearbyEntities.filter(e => e.type === 'food');

        // Update memory
        this.updateMemoryWith(threats, prey, food);

        // Determine new state
        const newState = this.determineState(threats, prey, food);
        
        if (newState !== this.aiState) {
            this.changeState(newState);
        }

        // Set target based on state
        this.setTarget(threats, prey, food);
    }

    /**
     * Determine AI state based on environment
     */
    determineState(threats, prey, food) {
        // Immediate threat - flee
        if (threats.length > 0) {
            const closestThreat = threats.reduce((closest, threat) => 
                this.distanceTo(threat) < this.distanceTo(closest) ? threat : closest
            );
            
            if (this.distanceTo(closestThreat) < this.visionRange * 0.6) {
                return 'fleeing';
            }
        }

        // Hunting opportunity
        if (prey.length > 0 && Math.random() < this.aggressiveness) {
            return 'hunting';
        }

        // Seeking food
        if (food.length > 0) {
            return 'seeking';
        }

        // Default to seeking
        return 'seeking';
    }

    /**
     * Change AI state
     */
    changeState(newState) {
        this.aiState = newState;
        this.lastStateChange = Date.now();
        this.target = null;
    }

    /**
     * Set target based on current state
     */
    setTarget(threats, prey, food) {
        switch (this.aiState) {
            case 'fleeing':
                this.target = this.findFleeTarget(threats);
                break;
            case 'hunting':
                this.target = this.findHuntTarget(prey);
                break;
            case 'seeking':
                this.target = this.findFoodTarget(food);
                break;
            default:
                this.target = this.findRandomTarget();
                break;
        }
    }

    /**
     * Execute current behavior
     */
    executeBehavior(deltaTime, physics, entities) {
        switch (this.aiState) {
            case 'fleeing':
                this.flee(physics);
                break;
            case 'hunting':
                this.hunt(physics);
                this.considerSplit(physics);
                break;
            case 'seeking':
                this.seek(physics);
                break;
            default:
                this.wander(physics);
                break;
        }

        // Consider ejecting mass strategically
        this.considerEject(physics, entities);
    }

    /**
     * Flee from threats
     */
    flee(physics) {
        if (!this.target) return;

        // Move away from threat
        const fleeX = this.x + (this.x - this.target.x) * 2;
        const fleeY = this.y + (this.y - this.target.y) * 2;
        
        this.setTarget(fleeX, fleeY);
    }

    /**
     * Hunt prey
     */
    hunt(physics) {
        if (!this.target || !this.target.alive) {
            this.target = null;
            return;
        }

        // Predict target movement
        const predictedX = this.target.x + this.target.vx * this.reactionTime / 1000;
        const predictedY = this.target.y + this.target.vy * this.reactionTime / 1000;
        
        // Add some inaccuracy based on difficulty
        const inaccuracy = (1 - this.accuracy) * 50;
        const targetX = predictedX + Utils.random(-inaccuracy, inaccuracy);
        const targetY = predictedY + Utils.random(-inaccuracy, inaccuracy);
        
        this.setTarget(targetX, targetY);
    }

    /**
     * Seek food
     */
    seek(physics) {
        if (!this.target) return;

        this.setTarget(this.target.x, this.target.y);
    }

    /**
     * Wander randomly
     */
    wander(physics) {
        // Change direction occasionally
        if (!this.target || Math.random() < 0.01) {
            this.target = this.findRandomTarget();
        }

        if (this.target) {
            this.setTarget(this.target.x, this.target.y);
        }
    }

    /**
     * Consider splitting to catch prey
     */
    considerSplit(physics) {
        if (!this.canSplit || !this.target) return;
        if (Math.random() > this.splitChance) return;

        const distance = this.distanceTo(this.target);
        const splitRange = this.radius * 3;

        if (distance < splitRange && distance > this.radius) {
            const direction = this.angleTo(this.target);
            return this.split(direction, physics);
        }

        return null;
    }

    /**
     * Consider ejecting mass strategically
     */
    considerEject(physics, entities) {
        if (!this.canEject) return;
        if (Math.random() > this.ejectChance) return;

        // Eject to escape or to feed smaller allies
        if (this.aiState === 'fleeing') {
            const direction = Math.random() * Math.PI * 2;
            return this.ejectMass(direction, physics);
        }

        return null;
    }

    /**
     * Find nearby entities within vision range
     */
    findNearbyEntities(entities) {
        return entities.filter(entity => 
            entity !== this && 
            entity.alive !== false &&
            this.distanceTo(entity) <= this.visionRange
        );
    }

    /**
     * Check if entity is a threat
     */
    isThreat(entity) {
        return (entity.type === 'player' || entity.type === 'bot') &&
               entity.mass > this.mass * 1.2;
    }

    /**
     * Check if entity is prey
     */
    isPrey(entity) {
        return (entity.type === 'player' || entity.type === 'bot') &&
               this.mass > entity.mass * 1.2;
    }

    /**
     * Find flee target (away from threats)
     */
    findFleeTarget(threats) {
        if (threats.length === 0) return null;

        // Find average position of threats
        let avgX = 0, avgY = 0;
        for (const threat of threats) {
            avgX += threat.x;
            avgY += threat.y;
        }
        avgX /= threats.length;
        avgY /= threats.length;

        // Return position away from threats
        return {
            x: this.x + (this.x - avgX),
            y: this.y + (this.y - avgY)
        };
    }

    /**
     * Find hunt target (closest prey)
     */
    findHuntTarget(prey) {
        if (prey.length === 0) return null;

        return prey.reduce((closest, current) => 
            this.distanceTo(current) < this.distanceTo(closest) ? current : closest
        );
    }

    /**
     * Find food target (closest food)
     */
    findFoodTarget(food) {
        if (food.length === 0) return null;

        return food.reduce((closest, current) => 
            this.distanceTo(current) < this.distanceTo(closest) ? current : closest
        );
    }

    /**
     * Find random target for wandering
     */
    findRandomTarget() {
        const angle = Math.random() * Math.PI * 2;
        const distance = Utils.random(50, 200);
        
        return {
            x: this.x + Math.cos(angle) * distance,
            y: this.y + Math.sin(angle) * distance
        };
    }

    /**
     * Update memory system
     */
    updateMemory() {
        const now = Date.now();
        
        // Remove old memories
        this.memory.threats = this.memory.threats.filter(m => now - m.timestamp < this.memoryDuration);
        this.memory.food = this.memory.food.filter(m => now - m.timestamp < this.memoryDuration);
        this.memory.prey = this.memory.prey.filter(m => now - m.timestamp < this.memoryDuration);
    }

    /**
     * Update memory with new information
     */
    updateMemoryWith(threats, prey, food) {
        const now = Date.now();
        
        // Add new threats
        for (const threat of threats) {
            this.memory.threats.push({
                entity: threat,
                timestamp: now
            });
        }

        // Add new prey
        for (const p of prey) {
            this.memory.prey.push({
                entity: p,
                timestamp: now
            });
        }

        // Add new food
        for (const f of food) {
            this.memory.food.push({
                entity: f,
                timestamp: now
            });
        }
    }
}

// Export for use in other modules
window.Bot = Bot;
